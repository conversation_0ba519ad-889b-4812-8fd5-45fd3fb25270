#!/usr/bin/env python3
import json
import mysql.connector
from flask import Flask, request, jsonify
from datetime import datetime

app = Flask(__name__)

@app.route('/mqtt/data', methods=['POST'])
def receive_mqtt_data():
    try:
        raw_data = request.get_data(as_text=True)
        print(f"Received: {raw_data}")
        
        # 手动解析 EMQX 发送的数据
        lines = raw_data.strip().split('\n')
        client_id = 'unknown'
        topic = 'unknown'
        payload = ''
        
        for line in lines:
            line = line.strip().rstrip(',')
            if '"client_id":' in line:
                client_id = line.split('"')[3]
            elif '"topic":' in line:
                topic = line.split('"')[3]
            elif '"payload":' in line:
                # 提取 payload 内容（去掉外层引号）
                start = line.find('"payload": "') + 12
                end = line.rfind('"')
                payload = line[start:end]
        
        print(f"Parsed - Client: {client_id}, Topic: {topic}, Payload: {payload}")
        
        # 连接数据库
        conn = mysql.connector.connect(
            host='127.0.0.1',
            port=3306,
            user='emqx',
            password='EmqxPass!123',
            database='emqx_data'
        )
        cursor = conn.cursor()
        
        sql = """INSERT INTO mqtt_messages 
                 (client_id, topic, payload, qos, retain, arrived) 
                 VALUES (%s, %s, %s, %s, %s, %s)"""
        
        values = (client_id, topic, payload, 0, 0, datetime.now())
        
        cursor.execute(sql, values)
        conn.commit()
        cursor.close()
        conn.close()
        
        print("Data inserted successfully")
        return jsonify({"status": "success"}), 200
        
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy"}), 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)
