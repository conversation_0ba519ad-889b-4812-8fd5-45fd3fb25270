#!/usr/bin/env python3
# Version: 1.0 | Status: Active | Description: 百度地图显示服务 (端口: 8081)
from flask import Flask, render_template_string, jsonify
import mysql.connector
import json
from datetime import datetime

app = Flask(__name__)

mysql_config = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'emqx',
    'password': 'EmqxPass!123',
    'database': 'emqx_data'
}

@app.route('/')
def dashboard():
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <title>MQTT 地理位置监控面板</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            padding: 15px;
        }
        .container { max-width: 1600px; margin: 0 auto; }
        
        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 2.2em;
        }
        
        .status {
            display: inline-block;
            padding: 6px 12px;
            background: #2ecc71;
            color: white;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.9em;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .map-panel {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .panel-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 20px;
            font-size: 1.2em;
            font-weight: bold;
        }
        
        #map-container {
            height: 500px;
            position: relative;
        }
        
        .info-panel {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .location-list {
            max-height: 500px;
            overflow-y: auto;
            padding: 0;
        }
        
        .location-item {
            padding: 15px 20px;
            border-bottom: 1px solid #ecf0f1;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .location-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }
        
        .location-item.active {
            background: linear-gradient(90deg, #3498db20, transparent);
            border-left: 4px solid #3498db;
        }
        
        .location-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 1.1em;
        }
        
        .location-coords {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .location-time {
            color: #95a5a6;
            font-size: 0.8em;
            margin-bottom: 8px;
        }
        
        .location-data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            border-left: 3px solid #3498db;
            max-height: 100px;
            overflow-y: auto;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 1em;
            font-weight: 500;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .map-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #7f8c8d;
            z-index: 1000;
        }
        
        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗺️ MQTT 地理位置监控面板</h1>
            <p>实时显示设备位置和数据分布 (百度地图)</p>
            <div class="status" id="status">正在连接...</div>
        </div>
        
        <div class="main-grid">
            <div class="map-panel">
                <div class="panel-header">
                    🌍 实时位置地图 <small style="color: #7f8c8d;">(使用高德地图瓦片)</small>
                </div>
                <div id="map-container">
                    <div class="map-loading" id="map-loading">
                        <div class="loading"></div>
                        <p style="margin-top: 15px;">正在加载百度地图...</p>
                    </div>
                </div>
            </div>
            
            <div class="info-panel">
                <div class="panel-header">
                    📍 位置设备列表
                </div>
                <div class="location-list" id="location-list">
                    <div style="padding: 40px 20px; text-align: center; color: #7f8c8d;">
                        <div class="loading"></div>
                        <p style="margin-top: 15px;">正在加载位置数据...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="total-locations">-</div>
                <div class="stat-label">位置设备</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-messages">-</div>
                <div class="stat-label">总消息数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="active-topics">-</div>
                <div class="stat-label">活跃主题</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="online-devices">-</div>
                <div class="stat-label">在线设备</div>
            </div>
        </div>
    </div>

    <script>
        let map;
        let markers = [];
        let locationData = [];
        let mapInitialized = false;
        
        // 直接初始化地图（不需要等待API）
        
        // 初始化Leaflet地图
        function initMap() {
            try {
                console.log('开始初始化地图...');

                // 创建地图实例
                map = L.map('map-container', {
                    minZoom: 3,   // 最小缩放级别
                    maxZoom: 18   // 最大缩放级别（可以放大到街道级别）
                }).setView([39.915, 116.404], 10);

                // 添加高德地图瓦片层（国内访问更快）
                L.tileLayer('https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', {
                    attribution: '© 高德地图',
                    subdomains: ['1', '2', '3', '4'],
                    maxZoom: 18  // 支持放大到街道级别
                }).addTo(map);

                // 隐藏加载提示
                document.getElementById('map-loading').style.display = 'none';
                mapInitialized = true;

                console.log('地图初始化完成');

                // 立即加载数据
                loadData();

            } catch (error) {
                console.error('地图初始化失败:', error);
                document.getElementById('map-loading').innerHTML =
                    '<p style="color: #e74c3c;">地图加载失败<br><small>请检查网络连接</small></p>';
            }
        }
        
        // 添加地图标记
        function addMapMarkers(locations) {
            if (!mapInitialized || !map) {
                console.log('地图未初始化，跳过标记添加');
                return;
            }

            // 清除旧标记
            markers.forEach(marker => map.removeLayer(marker));
            markers = [];

            if (locations.length === 0) {
                console.log('没有位置数据');
                return;
            }

            const points = [];

            locations.forEach((location, index) => {
                const lat = parseFloat(location.latitude);
                const lng = parseFloat(location.longitude);

                if (isNaN(lat) || isNaN(lng)) {
                    console.log('无效坐标:', location);
                    return;
                }

                // 创建标记
                const marker = L.marker([lat, lng]).addTo(map);

                // 创建弹出窗口内容
                const popupContent = `
                    <div style="padding: 5px; min-width: 200px;">
                        <h4 style="margin: 0 0 8px 0; color: #2c3e50;">${location.name}</h4>
                        <p style="margin: 4px 0; color: #7f8c8d;">
                            <strong>📍 位置:</strong> ${lat.toFixed(4)}, ${lng.toFixed(4)}
                        </p>
                        <p style="margin: 4px 0; color: #7f8c8d;">
                            <strong>🕒 更新:</strong> ${location.last_update}
                        </p>
                        <div style="margin-top: 8px; padding: 8px; background: #f8f9fa; border-radius: 4px; max-height: 150px; overflow-y: auto;">
                            <strong>📊 数据:</strong><br>
                            <pre style="font-size: 0.85em; margin: 4px 0; white-space: pre-wrap;">${JSON.stringify(location.latest_data, null, 2)}</pre>
                        </div>
                    </div>
                `;

                marker.bindPopup(popupContent);

                marker.on('click', function() {
                    highlightLocationItem(index);
                });

                markers.push(marker);
                points.push([lat, lng]);
            });

            // 自动调整地图视野
            if (points.length > 0) {
                const group = new L.featureGroup(markers);
                map.fitBounds(group.getBounds().pad(0.1));
            }
            
            console.log(`添加了 ${markers.length} 个标记`);
        }
        
        // 更新位置列表
        function updateLocationList(locations) {
            const listDiv = document.getElementById('location-list');
            
            if (locations.length === 0) {
                listDiv.innerHTML = '<div style="padding: 40px 20px; text-align: center; color: #7f8c8d;"><p>暂无位置数据<br><small>发送包含 latitude/longitude 的 MQTT 消息来显示位置</small></p></div>';
                return;
            }
            
            const html = locations.map((location, index) => `
                <div class="location-item" onclick="focusLocation(${index})" data-index="${index}">
                    <div class="location-name">${location.name}</div>
                    <div class="location-coords">
                        📍 ${parseFloat(location.latitude).toFixed(4)}, ${parseFloat(location.longitude).toFixed(4)}
                    </div>
                    <div class="location-time">
                        🕒 ${location.last_update}
                    </div>
                    <div class="location-data">
                        ${JSON.stringify(location.latest_data, null, 2)}
                    </div>
                </div>
            `).join('');
            
            listDiv.innerHTML = html;
        }
        
        // 聚焦到指定位置
        function focusLocation(index) {
            if (locationData[index] && markers[index] && mapInitialized) {
                const location = locationData[index];
                const lat = parseFloat(location.latitude);
                const lng = parseFloat(location.longitude);
                
                const point = new BMap.Point(lng, lat);
                map.centerAndZoom(point, 15);
                
                // 触发标记点击
                markers[index].dispatchEvent(new Event('click'));
                highlightLocationItem(index);
            }
        }
        
        // 高亮位置项
        function highlightLocationItem(index) {
            document.querySelectorAll('.location-item').forEach(item => {
                item.classList.remove('active');
            });
            
            const targetItem = document.querySelector(`[data-index="${index}"]`);
            if (targetItem) {
                targetItem.classList.add('active');
                targetItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
        
        // 更新统计数据
        function updateStats(data) {
            document.getElementById('total-locations').textContent = data.locations.length;
            document.getElementById('total-messages').textContent = data.stats.total_messages;
            document.getElementById('active-topics').textContent = data.stats.active_topics;
            document.getElementById('online-devices').textContent = data.stats.active_clients;
        }
        
        // 加载数据
        function loadData() {
            fetch('/api/map-data')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        locationData = data.locations;
                        updateStats(data);
                        updateLocationList(data.locations);
                        
                        if (mapInitialized) {
                            addMapMarkers(data.locations);
                        }
                        
                        document.getElementById('status').textContent = 
                            `在线 - ${new Date().toLocaleTimeString()} (${data.locations.length} 个位置)`;
                        document.getElementById('status').style.background = '#2ecc71';
                        
                        console.log('数据更新成功:', data.locations.length, '个位置');
                    } else {
                        throw new Error(data.message || '数据加载失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('status').textContent = '连接失败';
                    document.getElementById('status').style.background = '#e74c3c';
                });
        }
        
        // 页面加载完成后初始化
        window.onload = function() {
            console.log('页面加载完成，初始化地图...');
            initMap();

            // 每5秒自动更新数据
            setInterval(loadData, 5000);
        };
    </script>
</body>
</html>
    ''')

@app.route('/api/map-data')
def api_map_data():
    try:
        conn = mysql.connector.connect(**mysql_config)
        cursor = conn.cursor(dictionary=True)
        
        # 获取包含经纬度的消息
        cursor.execute("""
            SELECT id, client_id, topic, payload, arrived
            FROM mqtt_messages
            WHERE (payload LIKE '%"latitude"%' AND payload LIKE '%"longitude"%')
               OR (payload REGEXP '"lat(itude)?"|"lng"|"lon(gitude)?"')
            ORDER BY arrived DESC
            LIMIT 100
        """)
        
        location_messages = cursor.fetchall()
        
        # 解析位置数据
        locations = []
        location_dict = {}
        
        for msg in location_messages:
            try:
                payload = json.loads(msg['payload'])
                
                # 尝试提取经纬度
                lat = None
                lng = None
                
                # 常见的经纬度字段名
                lat_fields = ['latitude', 'lat', 'y']
                lng_fields = ['longitude', 'lng', 'lon', 'x']
                
                for field in lat_fields:
                    if field in payload and payload[field] is not None:
                        try:
                            lat = float(payload[field])
                            break
                        except (ValueError, TypeError):
                            continue
                
                for field in lng_fields:
                    if field in payload and payload[field] is not None:
                        try:
                            lng = float(payload[field])
                            break
                        except (ValueError, TypeError):
                            continue
                
                if lat is not None and lng is not None:
                    # 基本的坐标范围检查
                    if -90 <= lat <= 90 and -180 <= lng <= 180:
                        location_key = f"{msg['client_id']}_{msg['topic']}"
                        
                        if location_key not in location_dict or msg['arrived'] > location_dict[location_key]['last_update_raw']:
                            location_dict[location_key] = {
                                'name': f"{msg['client_id']} - {msg['topic'].split('/')[-1]}",
                                'latitude': lat,
                                'longitude': lng,
                                'latest_data': payload,
                                'last_update': msg['arrived'].strftime('%Y-%m-%d %H:%M:%S'),
                                'last_update_raw': msg['arrived'],
                                'client_id': msg['client_id'],
                                'topic': msg['topic']
                            }
            except (json.JSONDecodeError, ValueError, KeyError, TypeError):
                continue
        
        locations = list(location_dict.values())
        
        # 获取统计数据
        cursor.execute("SELECT COUNT(*) as total FROM mqtt_messages")
        total_messages = cursor.fetchone()['total']
        
        cursor.execute("SELECT COUNT(DISTINCT topic) as topics FROM mqtt_messages")
        active_topics = cursor.fetchone()['topics']
        
        cursor.execute("SELECT COUNT(DISTINCT client_id) as clients FROM mqtt_messages")
        active_clients = cursor.fetchone()['clients']
        
        cursor.close()
        conn.close()
        
        return {
            'status': 'success',
            'locations': locations,
            'stats': {
                'total_messages': total_messages,
                'active_topics': active_topics,
                'active_clients': active_clients
            },
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            'status': 'error',
            'message': str(e)
        }

if __name__ == '__main__':
    print("🗺️ 启动 MQTT 地理位置监控面板 (百度地图版本)...")
    print("📡 访问地址: http://**************:8081")
    print("🔑 使用百度地图 API Key: 0tb94kg40u0eN1PKDUHkvVQTjZmfus8Z")
    app.run(host='0.0.0.0', port=8081, debug=False)
