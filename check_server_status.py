#!/usr/bin/env python3
"""
服务器状态检查脚本
检查端口占用、运行的Python进程、数据库连接等
"""

import subprocess
import sys
import mysql.connector
import requests
from datetime import datetime

def run_command(cmd):
    """执行系统命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return "", str(e)

def check_ports():
    """检查端口占用情况"""
    print("🔍 检查端口占用情况...")
    ports_to_check = [8080, 8081, 3306, 1883, 18083]
    
    for port in ports_to_check:
        stdout, stderr = run_command(f"netstat -tlnp | grep :{port}")
        if stdout:
            print(f"✅ 端口 {port}: {stdout}")
        else:
            print(f"❌ 端口 {port}: 未占用")

def check_python_processes():
    """检查Python进程"""
    print("\n🐍 检查Python进程...")
    stdout, stderr = run_command("ps aux | grep python | grep -v grep")
    if stdout:
        for line in stdout.split('\n'):
            if line.strip():
                print(f"📋 {line}")
    else:
        print("❌ 没有找到Python进程")

def check_mysql_connection():
    """检查MySQL连接"""
    print("\n🗄️ 检查MySQL连接...")
    try:
        config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'emqx',
            'password': 'EmqxPass!123',
            'database': 'emqx_data'
        }
        
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"✅ MySQL连接成功，数据库表: {[table[0] for table in tables]}")
        
        # 检查最新数据
        cursor.execute("SELECT COUNT(*) FROM mqtt_messages")
        count = cursor.fetchone()[0]
        print(f"📊 mqtt_messages表中有 {count} 条记录")
        
        # 检查最新的几条记录
        cursor.execute("SELECT * FROM mqtt_messages ORDER BY created_at DESC LIMIT 3")
        recent_data = cursor.fetchall()
        print("📝 最新的3条记录:")
        for row in recent_data:
            print(f"   {row}")
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")

def check_web_services():
    """检查Web服务"""
    print("\n🌐 检查Web服务...")
    urls_to_check = [
        "http://127.0.0.1:8080",
        "http://127.0.0.1:8081",
        "http://127.0.0.1:18083"  # EMQX管理界面
    ]
    
    for url in urls_to_check:
        try:
            response = requests.get(url, timeout=5)
            print(f"✅ {url}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {url}: {e}")

def check_docker_containers():
    """检查Docker容器"""
    print("\n🐳 检查Docker容器...")
    stdout, stderr = run_command("docker ps")
    if stdout:
        print("📋 运行中的容器:")
        print(stdout)
    else:
        print("❌ 没有运行中的Docker容器或Docker未安装")

if __name__ == "__main__":
    print(f"🚀 服务器状态检查 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    check_ports()
    check_python_processes()
    check_mysql_connection()
    check_web_services()
    check_docker_containers()
    
    print("\n" + "=" * 60)
    print("✅ 检查完成！")
