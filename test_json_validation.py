#!/usr/bin/env python3
"""
JSON格式验证测试脚本
测试修复后的数据验证和ID计数问题
"""

import json
import requests
from datetime import datetime
import time

def test_invalid_json_formats(server_ip="localhost", port=5000):
    """测试各种无效的JSON格式"""
    url = f"http://{server_ip}:{port}/mqtt/data"
    
    print("🧪 JSON格式验证测试")
    print("=" * 60)
    print("目标: 验证无效JSON不会导致ID递增但数据丢失的问题")
    print("=" * 60)
    
    # 测试用例：各种无效的JSON格式
    test_cases = [
        {
            "name": "您提到的格式错误 - 多余逗号",
            "data": {
                "client_id": "test_device_001",
                "topic": "location/test_device_001",
                "payload": '{"CH4": 1110, "lat": 39.87127, "lng": 117.94614,}',  # 多余逗号
                "qos": 0,
                "timestamp": datetime.now().isoformat()
            },
            "should_fail": True
        },
        {
            "name": "缺少引号",
            "data": {
                "client_id": "test_device_002", 
                "topic": "location/test_device_002",
                "payload": '{CH4: 1110, lat: 39.87127, lng: 117.94614}',  # 缺少引号
                "qos": 0,
                "timestamp": datetime.now().isoformat()
            },
            "should_fail": True
        },
        {
            "name": "不匹配的括号",
            "data": {
                "client_id": "test_device_003",
                "topic": "location/test_device_003", 
                "payload": '{"CH4": 1110, "lat": 39.87127, "lng": 117.94614',  # 缺少}
                "qos": 0,
                "timestamp": datetime.now().isoformat()
            },
            "should_fail": True
        },
        {
            "name": "有效的JSON格式",
            "data": {
                "client_id": "test_device_004",
                "topic": "location/test_device_004",
                "payload": '{"CH4": 1110, "lat": 39.87127, "lng": 117.94614}',  # 正确格式
                "qos": 0,
                "timestamp": datetime.now().isoformat()
            },
            "should_fail": False
        },
        {
            "name": "空的payload",
            "data": {
                "client_id": "test_device_005",
                "topic": "location/test_device_005",
                "payload": '',  # 空payload
                "qos": 0,
                "timestamp": datetime.now().isoformat()
            },
            "should_fail": True
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📡 测试 {i}/5: {test_case['name']}")
        print(f"   Payload: {test_case['data']['payload']}")
        print(f"   预期结果: {'失败' if test_case['should_fail'] else '成功'}")
        
        try:
            response = requests.post(url, json=test_case['data'], timeout=10)
            
            if test_case['should_fail']:
                if response.status_code == 400:
                    print(f"   ✅ 正确拒绝了无效数据 (HTTP {response.status_code})")
                    result = "PASS - 正确拒绝"
                else:
                    print(f"   ❌ 应该拒绝但接受了 (HTTP {response.status_code})")
                    result = "FAIL - 错误接受"
            else:
                if response.status_code == 200:
                    print(f"   ✅ 正确接受了有效数据 (HTTP {response.status_code})")
                    result = "PASS - 正确接受"
                else:
                    print(f"   ❌ 应该接受但拒绝了 (HTTP {response.status_code})")
                    result = "FAIL - 错误拒绝"
            
            # 显示响应内容
            try:
                response_data = response.json()
                print(f"   响应: {response_data.get('message', 'N/A')}")
            except:
                print(f"   响应: {response.text[:100]}...")
                
            results.append({
                "test": test_case['name'],
                "result": result,
                "status_code": response.status_code
            })
            
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 网络错误: {e}")
            results.append({
                "test": test_case['name'],
                "result": "ERROR - 网络问题",
                "status_code": None
            })
        
        # 间隔发送
        if i < len(test_cases):
            time.sleep(1)
    
    return results

def check_database_consistency(server_ip="localhost", port=8082):
    """检查数据库一致性"""
    print(f"\n🔍 检查数据库一致性")
    print("=" * 40)
    
    try:
        # 获取统计数据
        response = requests.get(f"http://{server_ip}:{port}/api/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            total_count = stats.get('stats', {}).get('total', 0)
            print(f"📊 数据库总记录数: {total_count}")
            
            # 获取实际数据
            response = requests.get(f"http://{server_ip}:{port}/api/data", timeout=10)
            if response.status_code == 200:
                data = response.json()
                actual_count = len(data.get('data', []))
                print(f"📋 实际数据条数: {actual_count}")
                
                if total_count == actual_count:
                    print("✅ 数据库计数一致")
                    return True
                else:
                    print(f"❌ 数据库计数不一致! 统计:{total_count} vs 实际:{actual_count}")
                    return False
            else:
                print(f"❌ 获取数据失败: HTTP {response.status_code}")
                return False
        else:
            print(f"❌ 获取统计失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")
        return False

def main():
    print("🚀 JSON格式验证和ID计数修复测试")
    print("目标: 确保无效JSON不会导致ID递增但数据丢失")
    print("=" * 60)
    
    # 获取服务器信息
    server_ip = input("请输入服务器IP (默认localhost): ").strip() or "localhost"
    
    # 检查服务状态
    try:
        response = requests.get(f"http://{server_ip}:5000/health", timeout=5)
        if response.status_code == 200:
            print("✅ MQTT接收服务运行正常")
        else:
            print(f"❌ MQTT接收服务异常: HTTP {response.status_code}")
            return
    except:
        print("❌ 无法连接到MQTT接收服务")
        return
    
    # 检查初始数据库状态
    print(f"\n📊 检查初始数据库状态")
    initial_consistent = check_database_consistency(server_ip, 8082)
    
    # 执行测试
    print(f"\n🧪 开始JSON格式验证测试")
    results = test_invalid_json_formats(server_ip, 5000)
    
    # 等待数据处理
    print(f"\n⏳ 等待数据处理...")
    time.sleep(3)
    
    # 检查最终数据库状态
    print(f"\n📊 检查最终数据库状态")
    final_consistent = check_database_consistency(server_ip, 8082)
    
    # 总结结果
    print(f"\n" + "=" * 60)
    print("📋 测试结果总结:")
    print("=" * 60)
    
    for result in results:
        status = "✅" if "PASS" in result['result'] else "❌"
        print(f"{status} {result['test']}: {result['result']}")
    
    print(f"\n📊 数据库一致性:")
    print(f"   初始状态: {'✅ 一致' if initial_consistent else '❌ 不一致'}")
    print(f"   最终状态: {'✅ 一致' if final_consistent else '❌ 不一致'}")
    
    if final_consistent:
        print(f"\n🎉 修复成功! 无效JSON不再导致ID计数问题")
    else:
        print(f"\n⚠️ 仍存在问题，需要进一步检查")
    
    print(f"\n🔍 验证方法:")
    print(f"1. 访问监控界面: http://{server_ip}:8082")
    print(f"2. 清空数据库，然后发送无效JSON")
    print(f"3. 检查总记录数是否仍为0")
    print(f"4. 发送有效数据，检查计数是否正确")

if __name__ == "__main__":
    main()
