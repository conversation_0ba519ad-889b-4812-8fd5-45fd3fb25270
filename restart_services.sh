#!/bin/bash

# 天然气管道巡检系统 - 一键重启脚本
# 作者: AI Assistant
# 日期: 2025-08-13
# 用法: ./restart_services.sh [mqtt]  # 添加mqtt参数会同时启动MQTT接收器

# 检查是否需要启动MQTT接收器
START_MQTT=false
if [ "$1" = "mqtt" ]; then
    START_MQTT=true
fi

echo "🚀 开始重启天然气管道巡检系统服务..."
if [ "$START_MQTT" = true ]; then
    echo "📡 将同时启动MQTT接收器"
fi
echo "================================================"

# 设置工作目录
WORK_DIR="/opt/mqtt-to-mysql"
cd "$WORK_DIR" || {
    echo "❌ 错误: 无法进入工作目录 $WORK_DIR"
    exit 1
}

echo "📂 当前工作目录: $(pwd)"
echo ""

# 1. 停止所有相关进程
echo "🛑 正在停止现有服务..."
echo "----------------------------------------"

# 停止Web服务
echo "  停止 gas_pipeline_inspection.py..."
pkill -f gas_pipeline_inspection
sleep 2

# 停止MQTT接收器
echo "  停止 receiver_improved.py..."
pkill -f receiver_improved
sleep 2

# 停止其他可能的MQTT相关进程
echo "  停止其他MQTT相关进程..."
pkill -f receiver.py
pkill -f mqtt
sleep 1

echo "✅ 服务停止完成"
echo ""

# 2. 检查进程是否完全停止
echo "🔍 检查剩余进程..."
echo "----------------------------------------"
REMAINING=$(ps aux | grep -E "(gas_pipeline|receiver|mqtt)" | grep -v grep | grep -v restart_services)
if [ -n "$REMAINING" ]; then
    echo "⚠️  发现剩余进程:"
    echo "$REMAINING"
    echo ""
    echo "🔨 强制终止剩余进程..."
    pkill -9 -f gas_pipeline_inspection
    pkill -9 -f receiver
    sleep 2
else
    echo "✅ 所有进程已完全停止"
fi
echo ""

# 3. 启动服务
echo "🚀 正在启动服务..."
echo "----------------------------------------"

# 启动Web服务
echo "  启动 gas_pipeline_inspection.py..."
nohup python3 gas_pipeline_inspection.py > gas_pipeline.log 2>&1 &
WEB_PID=$!
echo "  Web服务 PID: $WEB_PID"

# 等待Web服务启动
sleep 3

# 检查Web服务是否启动成功
if ps -p $WEB_PID > /dev/null; then
    echo "✅ Web服务启动成功"
else
    echo "❌ Web服务启动失败，请检查日志"
    tail -20 gas_pipeline.log
    exit 1
fi

echo ""

# 4. 启动MQTT接收器（可选）
if [ "$START_MQTT" = true ]; then
    echo "📡 启动MQTT接收器..."
    echo "----------------------------------------"
    nohup python3 receiver_improved.py > receiver.log 2>&1 &
    MQTT_PID=$!
    echo "  MQTT接收器 PID: $MQTT_PID"

    # 等待MQTT服务启动
    sleep 2

    # 检查MQTT服务是否启动成功
    if ps -p $MQTT_PID > /dev/null; then
        echo "✅ MQTT接收器启动成功"
    else
        echo "❌ MQTT接收器启动失败，请检查日志"
        tail -10 receiver.log
    fi
else
    echo "📡 MQTT接收器选项:"
    echo "----------------------------------------"
    echo "  MQTT接收器未启动（避免测试时数据干扰）"
    echo "  如需启动MQTT接收器，请运行:"
    echo "  ./restart_services.sh mqtt"
    echo "  或手动运行: nohup python3 receiver_improved.py > receiver.log 2>&1 &"
fi
echo ""

# 5. 显示服务状态
echo "📊 当前服务状态:"
echo "----------------------------------------"
echo "正在运行的相关进程:"
ps aux | grep -E "(gas_pipeline|receiver)" | grep -v grep | grep -v restart_services

echo ""
echo "🌐 Web服务访问地址:"
echo "  本地: http://127.0.0.1:8082"
echo "  外网: http://**************:8082"
echo ""

# 6. 显示日志文件位置
echo "📝 日志文件位置:"
echo "----------------------------------------"
echo "  Web服务日志: $WORK_DIR/gas_pipeline.log"
if [ "$START_MQTT" = true ]; then
    echo "  MQTT服务日志: $WORK_DIR/receiver.log"
fi
echo "  实时查看Web日志: tail -f $WORK_DIR/gas_pipeline.log"
if [ "$START_MQTT" = true ]; then
    echo "  实时查看MQTT日志: tail -f $WORK_DIR/receiver.log"
fi
echo ""

echo "🎉 服务重启完成！"
echo "================================================"
echo ""
echo "💡 使用提示:"
echo "  只重启Web服务: ./restart_services.sh"
echo "  同时启动MQTT: ./restart_services.sh mqtt"
echo "  高级管理: ./manage_services.sh help"
