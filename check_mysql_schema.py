#!/usr/bin/env python3
"""
检查MySQL数据库表结构
"""

import mysql.connector
from datetime import datetime

def check_database_schema():
    """检查数据库表结构"""
    mysql_config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'emqx',
        'password': 'EmqxPass!123',
        'database': 'emqx_data'
    }
    
    try:
        conn = mysql.connector.connect(**mysql_config)
        cursor = conn.cursor()
        
        print("🗄️ 检查数据库表结构...")
        print("=" * 40)
        
        # 检查所有表
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"📊 数据库中的表: {[table[0] for table in tables]}")
        
        # 检查mqtt_messages表结构
        if ('mqtt_messages',) in tables:
            print(f"\n📋 mqtt_messages 表结构:")
            cursor.execute("DESCRIBE mqtt_messages")
            columns = cursor.fetchall()
            
            for col in columns:
                field_name = col[0]
                field_type = col[1]
                nullable = "NULL" if col[2] == "YES" else "NOT NULL"
                default = f"DEFAULT {col[4]}" if col[4] else ""
                print(f"  {field_name:<15} | {field_type:<20} | {nullable:<8} | {default}")
            
            # 检查数据样本
            print(f"\n📊 数据样本 (最新5条):")
            cursor.execute("SELECT * FROM mqtt_messages ORDER BY id DESC LIMIT 5")
            rows = cursor.fetchall()
            
            if rows:
                # 获取列名
                cursor.execute("SHOW COLUMNS FROM mqtt_messages")
                column_info = cursor.fetchall()
                column_names = [col[0] for col in column_info]
                
                print(f"列名: {column_names}")
                for i, row in enumerate(rows, 1):
                    print(f"记录{i}: {row}")
            else:
                print("❌ 表中没有数据")
        else:
            print("❌ mqtt_messages 表不存在")
            
            # 创建表的建议
            print("\n💡 建议创建表:")
            create_table_sql = """
            CREATE TABLE mqtt_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                client_id VARCHAR(255),
                topic VARCHAR(255),
                payload TEXT,
                qos INT,
                arrived TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_topic (topic),
                INDEX idx_arrived (arrived)
            );
            """
            print(create_table_sql)
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

if __name__ == "__main__":
    check_database_schema()
