# 天然气管道巡检监控系统 v2.0.0 - 详细部署文档

## 📋 系统概述

**天然气管道巡检监控系统 v2.0.0** 是一套基于 EMQX + MySQL + Flask + 高德地图的完整实时监控解决方案，专为天然气管道安全巡检设计。

### 🎯 核心功能
- 🌍 **实时地图监控** - 高德地图显示设备位置和气体浓度
- 📊 **多维数据分析** - 气体浓度、温度、湿度、压力等传感器数据
- 🔔 **实时消息推送** - 新数据到达时立即更新界面（5秒检查间隔）
- 🏷️ **智能状态管理** - 消息标记、已读、删除等状态持久化
- 📱 **响应式界面** - 支持PC和移动端访问
- 🔍 **多维度筛选** - 时间范围、气体浓度、设备类型等筛选功能

### 🏗️ 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MQTT设备      │───▶│   EMQX Broker   │───▶│ receiver_improved│
│  (传感器数据)   │    │  (消息代理)     │    │   (数据处理)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
┌─────────────────┐    ┌─────────────────┐           │
│   Web监控界面   │◀───│   MySQL数据库   │◀──────────┘
│(gas_pipeline_   │    │  (数据存储)     │
│ inspection.py)  │    │                 │
└─────────────────┘    └─────────────────┘
```

## 📦 部署包内容

### 🌟 核心服务文件
- `gas_pipeline_inspection.py` - **主要Web监控界面** (端口: 8082)
- `receiver_improved.py` - **MQTT数据接收服务** (端口: 5000)
- `manage_services.sh` - **服务管理脚本**
- `clear_database.py` - **数据库清空工具**

### 🚀 部署工具
- `deploy_production.sh` - **一键部署脚本** ⭐
- `quick_start.sh` - **快速启动脚本**

### 📖 文档资料
- `README.md` - **项目基础文档**
- `DEPLOYMENT_PACKAGE.md` - **部署说明**
- `TECHNICAL_PAPER.md` - **技术论文文档**
- `PACKAGE_INFO.txt` - **部署包信息**

## 🔧 系统要求

### 💻 硬件要求
| 组件 | 最低配置 | 推荐配置 |
|------|----------|----------|
| CPU | 2核心 | 4核心+ |
| 内存 | 4GB | 8GB+ |
| 磁盘 | 20GB可用空间 | 50GB+ |
| 网络 | 10Mbps | 100Mbps+ |

### 🖥️ 软件要求
| 软件 | 版本要求 | 说明 |
|------|----------|------|
| 操作系统 | Ubuntu 18.04+ / CentOS 7+ | 64位Linux系统 |
| Python | 3.6+ | 建议3.8+ |
| MySQL | 5.7+ / 8.0+ | 数据库服务 |
| Docker | 20.10+ | 用于EMQX容器 |

### 🌐 网络端口
需要开放以下端口：
- **5000** - MQTT数据接收服务
- **8082** - Web监控界面
- **1883** - MQTT协议端口
- **18083** - EMQX管理界面

## 🚀 快速部署指南

### 第一步：上传部署包

1. **上传压缩包到服务器**
   ```bash
   # 使用SCP上传
   scp gas-pipeline-monitor-v2.0.0.tar.gz root@服务器IP:/tmp/
   
   # 或使用SFTP
   sftp root@服务器IP
   put gas-pipeline-monitor-v2.0.0.tar.gz /tmp/
   quit
   ```

2. **登录服务器并解压**
   ```bash
   # SSH登录服务器
   ssh root@服务器IP
   
   # 解压部署包
   cd /tmp
   tar -xzf gas-pipeline-monitor-v2.0.0.tar.gz
   cd gas-pipeline-monitor-v2.0.0
   ```

### 第二步：执行一键部署

```bash
# 设置执行权限
chmod +x deploy_production.sh

# 执行一键部署
sudo ./deploy_production.sh
```

### 第三步：按提示完成配置

部署脚本会自动完成以下操作：
1. ✅ **环境检查** - 检查Python、MySQL、Docker等依赖
2. ✅ **依赖安装** - 自动安装所需的Python包
3. ✅ **备份现有** - 自动备份现有部署（如果存在）
4. ✅ **目录创建** - 创建 `/opt/mqtt-to-mysql` 项目目录
5. ✅ **文件部署** - 复制核心文件并设置权限
6. ✅ **数据库配置** - 配置MySQL数据库和表结构
7. ✅ **EMQX部署** - 自动部署EMQX Docker容器
8. ✅ **系统服务** - 配置systemd服务，支持开机自启
9. ✅ **服务启动** - 启动所有服务
10. ✅ **部署验证** - 验证服务状态和端口监听

## 🔧 详细部署步骤

### 步骤1：环境准备

#### 1.1 更新系统包
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

#### 1.2 安装基础依赖
```bash
# Ubuntu/Debian
sudo apt install -y python3 python3-pip mysql-client curl wget

# CentOS/RHEL
sudo yum install -y python3 python3-pip mysql curl wget
```

#### 1.3 安装Docker
```bash
# 使用官方安装脚本
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 启动Docker服务
sudo systemctl enable docker
sudo systemctl start docker

# 验证安装
docker --version
```

### 步骤2：数据库配置

#### 2.1 MySQL安装（如果未安装）
```bash
# Ubuntu/Debian
sudo apt install -y mysql-server

# CentOS/RHEL
sudo yum install -y mysql-server

# 启动MySQL服务
sudo systemctl enable mysql
sudo systemctl start mysql
```

#### 2.2 创建数据库和用户
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE emqx_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'emqx'@'%' IDENTIFIED BY 'EmqxPass!123';
CREATE USER 'emqx'@'localhost' IDENTIFIED BY 'EmqxPass!123';

-- 授权
GRANT ALL PRIVILEGES ON emqx_data.* TO 'emqx'@'%';
GRANT ALL PRIVILEGES ON emqx_data.* TO 'emqx'@'localhost';
FLUSH PRIVILEGES;

-- 创建消息表
USE emqx_data;
CREATE TABLE mqtt_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id VARCHAR(255),
    topic VARCHAR(255),
    payload TEXT,
    arrived TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    qos INT DEFAULT 0,
    retain BOOLEAN DEFAULT FALSE,
    INDEX idx_arrived (arrived),
    INDEX idx_topic (topic),
    INDEX idx_client_id (client_id)
);

-- 验证创建
DESCRIBE mqtt_messages;
EXIT;
```

#### 2.3 测试数据库连接
```bash
# 测试连接
mysql -u emqx -pEmqxPass!123 emqx_data -e "SELECT COUNT(*) FROM mqtt_messages;"
```

### 步骤3：部署核心服务

#### 3.1 创建项目目录
```bash
sudo mkdir -p /opt/mqtt-to-mysql
sudo chown -R $USER:$USER /opt/mqtt-to-mysql
cd /opt/mqtt-to-mysql
```

#### 3.2 复制核心文件
```bash
# 从解压目录复制文件
cp /tmp/gas-pipeline-monitor-v2.0.0/gas_pipeline_inspection.py ./
cp /tmp/gas-pipeline-monitor-v2.0.0/receiver_improved.py ./
cp /tmp/gas-pipeline-monitor-v2.0.0/manage_services.sh ./
cp /tmp/gas-pipeline-monitor-v2.0.0/clear_database.py ./
cp /tmp/gas-pipeline-monitor-v2.0.0/README.md ./

# 设置执行权限
chmod +x manage_services.sh
chmod +x clear_database.py
```

#### 3.3 安装Python依赖
```bash
pip3 install flask mysql-connector-python pymysql requests
```

### 步骤4：部署EMQX

#### 4.1 启动EMQX容器
```bash
docker run -d --name emqx \
    --restart=always \
    -p 1883:1883 \
    -p 8083:8083 \
    -p 8084:8084 \
    -p 8883:8883 \
    -p 18083:18083 \
    emqx/emqx:latest
```

#### 4.2 等待EMQX启动
```bash
# 等待30秒让EMQX完全启动
sleep 30

# 检查EMQX状态
docker ps | grep emqx
curl -s http://localhost:18083 > /dev/null && echo "EMQX启动成功" || echo "EMQX启动失败"
```

### 步骤5：配置系统服务

#### 5.1 创建systemd服务文件
```bash
sudo tee /etc/systemd/system/gas-pipeline-monitor.service > /dev/null << EOF
[Unit]
Description=Gas Pipeline Monitoring System
After=network.target mysql.service docker.service
Requires=mysql.service docker.service

[Service]
Type=forking
User=root
WorkingDirectory=/opt/mqtt-to-mysql
ExecStart=/opt/mqtt-to-mysql/manage_services.sh start-all
ExecStop=/opt/mqtt-to-mysql/manage_services.sh stop-all
ExecReload=/opt/mqtt-to-mysql/manage_services.sh restart-all
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
```

#### 5.2 启用系统服务
```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启用服务（开机自启）
sudo systemctl enable gas-pipeline-monitor.service

# 启动服务
sudo systemctl start gas-pipeline-monitor.service
```

## 🌐 EMQX配置指南

### 第一步：访问EMQX管理界面

1. **打开浏览器访问**
   ```
   http://您的服务器IP:18083
   ```

2. **登录信息**
   ```
   用户名: admin
   密码: public
   ```

### 第二步：创建HTTP连接器

1. **进入连接器管理**
   - 点击左侧菜单 **"集成"**
   - 选择 **"连接器"**
   - 点击 **"创建连接器"**

2. **选择连接器类型**
   - 选择 **"HTTP Server"**

3. **配置连接器参数**
   ```
   名称: mysql-http-bridge
   URL: http://您的服务器IP:5000/mqtt/data
   方法: POST
   请求头: Content-Type: application/json
   ```

4. **测试连接**
   - 点击 **"测试连接"** 按钮
   - 确保显示 **"连接成功"**

5. **保存配置**
   - 点击 **"创建"** 按钮

### 第三步：创建规则和动作

1. **进入规则管理**
   - 点击左侧菜单 **"集成"**
   - 选择 **"规则"**
   - 点击 **"创建规则"**

2. **配置规则**
   ```
   规则名称: mqtt-mysql
   SQL语句: SELECT clientid as client_id, topic, payload, qos, retain, timestamp as arrived FROM "location/+"
   ```

3. **添加动作**
   - 在 **"动作"** 部分点击 **"添加动作"**
   - 选择之前创建的 **"mysql-http-bridge"** 连接器

4. **配置请求体模板**
   ```json
   {
     "client_id": "${client_id}",
     "topic": "${topic}",
     "payload": "${payload}",
     "qos": ${qos},
     "retain": ${retain},
     "timestamp": "${timestamp}"
   }
   ```

5. **保存规则**
   - 点击 **"创建"** 按钮

## 🔧 服务管理

### 使用管理脚本

系统提供了完整的服务管理脚本 `manage_services.sh`：

```bash
cd /opt/mqtt-to-mysql

# 查看所有可用命令
./manage_services.sh help

# 启动服务
./manage_services.sh start-all      # 启动所有服务
./manage_services.sh start-web      # 仅启动Web服务
./manage_services.sh start-mqtt     # 仅启动MQTT服务

# 停止服务
./manage_services.sh stop-all       # 停止所有服务
./manage_services.sh stop-web       # 仅停止Web服务
./manage_services.sh stop-mqtt      # 仅停止MQTT服务

# 重启服务
./manage_services.sh restart-all    # 重启所有服务
./manage_services.sh restart-web    # 仅重启Web服务
./manage_services.sh restart-mqtt   # 仅重启MQTT服务

# 查看状态
./manage_services.sh status         # 查看服务状态

# 查看日志
./manage_services.sh logs-web       # 查看Web服务日志
./manage_services.sh logs-mqtt      # 查看MQTT服务日志
```

### 使用系统服务

```bash
# 启动系统服务
sudo systemctl start gas-pipeline-monitor

# 停止系统服务
sudo systemctl stop gas-pipeline-monitor

# 重启系统服务
sudo systemctl restart gas-pipeline-monitor

# 查看服务状态
sudo systemctl status gas-pipeline-monitor

# 查看服务日志
sudo journalctl -u gas-pipeline-monitor -f

# 启用开机自启
sudo systemctl enable gas-pipeline-monitor

# 禁用开机自启
sudo systemctl disable gas-pipeline-monitor
```

## 🧪 系统测试验证

### 第一步：检查服务状态

#### 1.1 检查端口监听
```bash
# 检查所有相关端口
netstat -tlnp | grep -E "(5000|8082|1883|18083)"

# 预期输出：
# tcp 0.0.0.0:5000  LISTEN  (receiver_improved.py)
# tcp 0.0.0.0:8082  LISTEN  (gas_pipeline_inspection.py)
# tcp 0.0.0.0:1883  LISTEN  (emqx)
# tcp 0.0.0.0:18083 LISTEN  (emqx)
```

#### 1.2 检查进程状态
```bash
# 检查Python进程
ps aux | grep -E "(gas_pipeline|receiver)" | grep -v grep

# 检查Docker容器
docker ps | grep emqx
```

#### 1.3 检查服务健康状态
```bash
# 测试MQTT接收服务
curl http://localhost:5000/health
# 预期输出：{"status": "healthy", "service": "MQTT Data Receiver"}

# 测试Web界面
curl -I http://localhost:8082
# 预期输出：HTTP/1.1 200 OK
```

### 第二步：测试数据流

#### 2.1 安装MQTT客户端
```bash
# Ubuntu/Debian
sudo apt install -y mosquitto-clients

# CentOS/RHEL
sudo yum install -y mosquitto
```

#### 2.2 发送测试数据
```bash
# 发送测试MQTT消息
mosquitto_pub -h localhost -t "location/test001" -m '{
  "lat": 39.920,
  "lng": 116.410,
  "gas": 2500,
  "temp": 28.0,
  "humidity": 70,
  "pressure": 1012.8,
  "device_id": "test001",
  "name": "测试设备"
}'
```

#### 2.3 验证数据接收
```bash
# 检查数据库记录
mysql -u emqx -pEmqxPass!123 emqx_data -e "
SELECT id, client_id, topic, arrived
FROM mqtt_messages
ORDER BY id DESC
LIMIT 5;"

# 检查MQTT服务日志
tail -20 /opt/mqtt-to-mysql/receiver.log | grep "收到MQTT数据"

# 检查EMQX规则统计
# 访问 http://服务器IP:18083 → 集成 → 规则 → 查看统计
```

### 第三步：Web界面测试

#### 3.1 访问监控界面
```
浏览器访问: http://服务器IP:8082
```

#### 3.2 功能测试清单
- ✅ **地图加载** - 高德地图正常显示
- ✅ **数据显示** - 能看到测试设备数据
- ✅ **实时刷新** - 新数据自动更新
- ✅ **地图标记** - 设备位置正确显示
- ✅ **数据筛选** - 时间、浓度筛选功能
- ✅ **状态管理** - 标记、删除功能
- ✅ **响应式设计** - 移动端访问正常

## 🔍 故障排除指南

### 常见问题1：服务启动失败

#### 问题症状
```
❌ gas_pipeline_inspection.py 启动失败
❌ receiver_improved.py 启动失败
```

#### 诊断步骤
```bash
# 1. 检查端口占用
lsof -i :5000
lsof -i :8082

# 2. 查看错误日志
tail -50 /opt/mqtt-to-mysql/gas_pipeline.log
tail -50 /opt/mqtt-to-mysql/receiver.log

# 3. 检查Python依赖
python3 -c "import flask, mysql.connector, pymysql"
```

#### 解决方案
```bash
# 清理端口占用
sudo fuser -k 5000/tcp
sudo fuser -k 8082/tcp

# 强制清理进程
pkill -9 -f gas_pipeline_inspection
pkill -9 -f receiver_improved

# 重新安装依赖
pip3 install --upgrade flask mysql-connector-python pymysql

# 重新启动服务
cd /opt/mqtt-to-mysql
./manage_services.sh restart-all
```

### 常见问题2：数据不显示

#### 问题症状
```
- Web界面显示"正在加载地图数据..."
- 数据库中无记录
- EMQX规则统计显示失败
```

#### 诊断步骤
```bash
# 1. 检查EMQX连接器状态
curl http://localhost:5000/health

# 2. 检查EMQX规则统计
# 访问 http://服务器IP:18083 → 集成 → 规则

# 3. 检查数据库连接
mysql -u emqx -pEmqxPass!123 emqx_data -e "SELECT COUNT(*) FROM mqtt_messages;"

# 4. 查看MQTT服务日志
tail -f /opt/mqtt-to-mysql/receiver.log
```

#### 解决方案
```bash
# 1. 修复EMQX连接器URL
# 访问 http://服务器IP:18083
# 集成 → 连接器 → 编辑 mysql-http-bridge
# 确保URL为: http://服务器IP:5000/mqtt/data (使用实际IP，不是localhost)

# 2. 重启EMQX容器
docker restart emqx

# 3. 重启MQTT接收服务
cd /opt/mqtt-to-mysql
./manage_services.sh restart-mqtt
```

### 常见问题3：EMQX连接失败

#### 问题症状
```
- EMQX管理界面无法访问
- MQTT消息发送失败
- Docker容器异常
```

#### 诊断步骤
```bash
# 1. 检查Docker状态
docker ps | grep emqx
docker logs emqx

# 2. 检查端口监听
netstat -tlnp | grep -E "(1883|18083)"

# 3. 检查防火墙
sudo ufw status
```

#### 解决方案
```bash
# 1. 重启EMQX容器
docker restart emqx

# 2. 如果容器不存在，重新创建
docker rm -f emqx
docker run -d --name emqx \
    --restart=always \
    -p 1883:1883 \
    -p 8083:8083 \
    -p 8084:8084 \
    -p 8883:8883 \
    -p 18083:18083 \
    emqx/emqx:latest

# 3. 配置防火墙
sudo ufw allow 1883
sudo ufw allow 18083
```

### 常见问题4：数据库连接问题

#### 问题症状
```
- 数据库连接失败
- 权限错误
- 表不存在
```

#### 诊断步骤
```bash
# 1. 测试数据库连接
mysql -u emqx -pEmqxPass!123 -h 127.0.0.1 emqx_data

# 2. 检查用户权限
mysql -u root -p -e "SELECT User, Host FROM mysql.user WHERE User='emqx';"

# 3. 检查表结构
mysql -u emqx -pEmqxPass!123 emqx_data -e "SHOW TABLES;"
```

#### 解决方案
```bash
# 1. 重新创建用户和权限
mysql -u root -p << EOF
DROP USER IF EXISTS 'emqx'@'%';
DROP USER IF EXISTS 'emqx'@'localhost';
CREATE USER 'emqx'@'%' IDENTIFIED BY 'EmqxPass!123';
CREATE USER 'emqx'@'localhost' IDENTIFIED BY 'EmqxPass!123';
GRANT ALL PRIVILEGES ON emqx_data.* TO 'emqx'@'%';
GRANT ALL PRIVILEGES ON emqx_data.* TO 'emqx'@'localhost';
FLUSH PRIVILEGES;
EOF

# 2. 重新创建表（如果需要）
mysql -u emqx -pEmqxPass!123 emqx_data << EOF
CREATE TABLE IF NOT EXISTS mqtt_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id VARCHAR(255),
    topic VARCHAR(255),
    payload TEXT,
    arrived TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    qos INT DEFAULT 0,
    retain BOOLEAN DEFAULT FALSE,
    INDEX idx_arrived (arrived),
    INDEX idx_topic (topic),
    INDEX idx_client_id (client_id)
);
EOF
```

## 🔒 安全配置建议

### 1. 修改默认密码
```bash
# 修改EMQX管理员密码
# 访问 http://服务器IP:18083
# 系统 → 用户 → 修改admin用户密码

# 修改MySQL密码
mysql -u root -p -e "ALTER USER 'emqx'@'%' IDENTIFIED BY '新密码';"
```

### 2. 配置防火墙
```bash
# 启用UFW防火墙
sudo ufw enable

# 只开放必要端口
sudo ufw allow ssh
sudo ufw allow 5000
sudo ufw allow 8082
sudo ufw allow 1883
sudo ufw allow 18083

# 查看防火墙状态
sudo ufw status
```

### 3. 启用SSL/TLS（可选）
```bash
# 为Web界面配置HTTPS
# 1. 获取SSL证书（Let's Encrypt）
# 2. 配置Nginx反向代理
# 3. 修改EMQX配置启用SSL
```

## 📊 性能监控

### 系统资源监控
```bash
# 查看系统资源使用
htop

# 查看磁盘使用
df -h

# 查看内存使用
free -h

# 查看网络连接
ss -tulnp | grep -E "(5000|8082|1883|18083)"
```

### 应用性能监控
```bash
# 查看服务进程资源使用
ps aux | grep -E "(gas_pipeline|receiver)" | grep -v grep

# 查看数据库性能
mysql -u emqx -pEmqxPass!123 emqx_data -e "SHOW PROCESSLIST;"

# 查看EMQX统计信息
# 访问 http://服务器IP:18083 → 监控 → 统计
```

## 📋 维护建议

### 日常维护
```bash
# 1. 定期清理日志
find /opt/mqtt-to-mysql -name "*.log" -size +100M -delete

# 2. 清理旧数据（保留30天）
mysql -u emqx -pEmqxPass!123 emqx_data -e "
DELETE FROM mqtt_messages
WHERE arrived < DATE_SUB(NOW(), INTERVAL 30 DAY);"

# 3. 优化数据库
mysql -u emqx -pEmqxPass!123 emqx_data -e "OPTIMIZE TABLE mqtt_messages;"

# 4. 检查服务状态
./manage_services.sh status
```

### 备份策略
```bash
# 1. 数据库备份
mysqldump -u emqx -pEmqxPass!123 emqx_data > backup_$(date +%Y%m%d).sql

# 2. 配置文件备份
tar -czf config_backup_$(date +%Y%m%d).tar.gz /opt/mqtt-to-mysql

# 3. 自动备份脚本
cat > /opt/mqtt-to-mysql/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/backups"
mkdir -p $BACKUP_DIR
mysqldump -u emqx -pEmqxPass!123 emqx_data > $BACKUP_DIR/db_$(date +%Y%m%d_%H%M%S).sql
find $BACKUP_DIR -name "db_*.sql" -mtime +7 -delete
EOF

chmod +x /opt/mqtt-to-mysql/backup.sh

# 4. 添加到定时任务
echo "0 2 * * * /opt/mqtt-to-mysql/backup.sh" | sudo crontab -
```

## 🎯 访问地址总览

部署完成后，可通过以下地址访问系统：

| 服务 | 地址 | 用途 | 认证 |
|------|------|------|------|
| **监控系统** | http://服务器IP:8082 | 主要监控界面 | 无 |
| **EMQX管理** | http://服务器IP:18083 | EMQX配置管理 | admin/public |
| **健康检查** | http://服务器IP:5000/health | 服务状态检查 | 无 |
| **API接口** | http://服务器IP:5000/api/stats | 统计数据API | 无 |

## 📞 技术支持

### 获取帮助
- 📖 查看项目文档：`cat /opt/mqtt-to-mysql/README.md`
- 📋 查看部署包信息：`cat PACKAGE_INFO.txt`
- 📝 查看技术论文：`cat TECHNICAL_PAPER.md`

### 常用诊断命令
```bash
# 一键状态检查
cd /opt/mqtt-to-mysql && ./manage_services.sh status

# 查看所有日志
tail -f /opt/mqtt-to-mysql/*.log

# 完整系统重启
sudo systemctl restart gas-pipeline-monitor
```

---

**部署包版本**: v2.0.0
**文档版本**: 详细版 v1.0
**最后更新**: 2025-08-13
**适用环境**: 生产环境
**技术支持**: BRIDGES
**系统架构**: EMQX + MySQL + Flask + 高德地图
