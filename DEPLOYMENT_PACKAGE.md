# 天然气管道巡检监控系统 - 生产环境部署包

## 📦 部署包内容

### 🌟 核心服务文件
- `gas_pipeline_inspection.py` - **主要Web监控界面** (端口: 8082)
- `receiver_improved.py` - **MQTT数据接收服务** (端口: 5000)
- `manage_services.sh` - **服务管理脚本**
- `clear_database.py` - **数据库清空工具**
- `README.md` - **项目文档**

### 🚀 部署脚本
- `deploy_production.sh` - **一键部署脚本** ⭐
- `DEPLOYMENT_PACKAGE.md` - **本说明文档**

## 🎯 一键部署使用方法

### 第一步：准备部署文件
```bash
# 1. 将所有文件上传到服务器的同一目录
# 2. 确保包含以下核心文件：
ls -la
# 应该看到：
# gas_pipeline_inspection.py
# receiver_improved.py
# manage_services.sh
# clear_database.py
# deploy_production.sh
# README.md
```

### 第二步：执行一键部署
```bash
# 设置执行权限
chmod +x deploy_production.sh

# 运行部署脚本
sudo ./deploy_production.sh
```

### 第三步：按提示完成配置
部署脚本会自动完成以下操作：
1. ✅ 检查系统要求并安装依赖
2. ✅ 备份现有部署（如果存在）
3. ✅ 创建项目目录 `/opt/mqtt-to-mysql`
4. ✅ 部署核心服务文件
5. ✅ 配置MySQL数据库
6. ✅ 部署EMQX Docker容器
7. ✅ 配置系统服务（开机自启）
8. ✅ 启动所有服务
9. ✅ 验证部署结果

## 🔧 部署后配置

### EMQX连接器配置 ⚠️ **重要**
部署完成后，需要手动配置EMQX连接器：

1. **访问EMQX管理界面**
   ```
   http://您的服务器IP:18083
   用户名: admin
   密码: public
   ```

2. **创建HTTP连接器**
   - 进入 **集成** → **连接器** → **创建连接器**
   - 选择 **HTTP Server**
   - 配置信息：
     - 名称: `mysql-http-bridge`
     - URL: `http://您的服务器IP:5000/mqtt/data` ⚠️ **使用实际IP**
     - 方法: `POST`
     - 请求头: `Content-Type: application/json`

3. **创建规则和动作**
   - 进入 **集成** → **规则** → **创建规则**
   - 规则名称: `mqtt-mysql`
   - SQL语句: `SELECT clientid as client_id, topic, payload, qos, retain, timestamp as arrived FROM "location/+"`
   - 添加动作: 选择 `mysql-http-bridge` 连接器
   - 请求体模板:
   ```json
   {
     "client_id": "${client_id}",
     "topic": "${topic}",
     "payload": "${payload}",
     "qos": ${qos},
     "retain": ${retain},
     "timestamp": "${timestamp}"
   }
   ```

## 🌐 访问地址

部署完成后，可以通过以下地址访问系统：

- **🌟 监控系统主界面**: http://您的服务器IP:8082
- **EMQX管理界面**: http://您的服务器IP:18083
- **健康检查接口**: http://您的服务器IP:5000/health

## 🔧 服务管理命令

### 使用管理脚本
```bash
cd /opt/mqtt-to-mysql

# 查看服务状态
./manage_services.sh status

# 启动所有服务
./manage_services.sh start-all

# 停止所有服务
./manage_services.sh stop-all

# 重启所有服务
./manage_services.sh restart-all

# 查看日志
./manage_services.sh logs-web    # Web服务日志
./manage_services.sh logs-mqtt   # MQTT服务日志
```

### 使用系统服务
```bash
# 启动系统服务
systemctl start gas-pipeline-monitor

# 停止系统服务
systemctl stop gas-pipeline-monitor

# 重启系统服务
systemctl restart gas-pipeline-monitor

# 查看服务状态
systemctl status gas-pipeline-monitor

# 查看服务日志
journalctl -u gas-pipeline-monitor -f
```

## 🧪 测试部署结果

### 1. 检查服务状态
```bash
# 检查端口监听
netstat -tlnp | grep -E "(5000|8082|1883|18083)"

# 检查进程
ps aux | grep -E "(gas_pipeline|receiver)" | grep -v grep

# 检查Docker容器
docker ps | grep emqx
```

### 2. 测试HTTP接口
```bash
# 测试MQTT接收服务
curl http://localhost:5000/health

# 测试Web界面
curl -I http://localhost:8082
```

### 3. 发送测试数据
```bash
# 安装MQTT客户端
apt install -y mosquitto-clients

# 发送测试消息
mosquitto_pub -h localhost -t "location/test001" -m '{
  "lat": 39.920,
  "lng": 116.410,
  "gas": 2500,
  "temp": 28.0,
  "humidity": 70,
  "pressure": 1012.8,
  "device_id": "test001",
  "name": "测试设备"
}'
```

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查端口占用
lsof -i :5000
lsof -i :8082

# 强制清理端口
sudo fuser -k 5000/tcp
sudo fuser -k 8082/tcp

# 重新启动
cd /opt/mqtt-to-mysql
./manage_services.sh restart-all
```

#### 2. EMQX连接失败
```bash
# 检查Docker状态
docker ps | grep emqx
docker logs emqx

# 重启EMQX
docker restart emqx
```

#### 3. 数据库连接问题
```bash
# 测试数据库连接
mysql -u emqx -pEmqxPass!123 emqx_data -e "SELECT COUNT(*) FROM mqtt_messages;"

# 检查数据库服务
systemctl status mysql
```

### 日志查看
```bash
# 查看部署日志
tail -f /var/log/syslog | grep gas-pipeline

# 查看应用日志
cd /opt/mqtt-to-mysql
tail -f gas_pipeline.log
tail -f receiver.log
```

## 📋 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **磁盘**: 20GB以上可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **操作系统**: Ubuntu 18.04+ / CentOS 7+
- **Python**: 3.6+
- **MySQL**: 5.7+ / 8.0+
- **Docker**: 20.10+

### 网络端口
需要开放以下端口：
- **5000**: MQTT数据接收服务
- **8082**: Web监控界面
- **1883**: MQTT协议端口
- **18083**: EMQX管理界面

## 🔒 安全建议

1. **修改默认密码**
   - EMQX管理界面密码
   - MySQL数据库密码

2. **配置防火墙**
   ```bash
   # 只开放必要端口
   ufw allow 5000
   ufw allow 8082
   ufw allow 1883
   ufw allow 18083
   ufw enable
   ```

3. **启用HTTPS**（可选）
   - 配置SSL证书
   - 使用反向代理（Nginx）

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看部署日志和错误信息
2. 检查系统要求是否满足
3. 确认网络配置是否正确
4. 参考故障排除章节

---

**部署包版本**: 2.0.0  
**最后更新**: 2025-08-13  
**适用环境**: 生产环境  
**维护者**: BRIDGES
