#!/usr/bin/env python3
"""
EMQX HTTP Bridge 配置脚本
用于配置EMQX将MQTT消息转发到我们的接收服务
"""

import requests
import json
import base64
from datetime import datetime

class EMQXBridgeConfig:
    def __init__(self, emqx_host="127.0.0.1", emqx_port=18083, username="admin", password="public"):
        self.base_url = f"http://{emqx_host}:{emqx_port}/api/v5"
        self.auth = (username, password)
        
    def get_bridges(self):
        """获取现有的桥接配置"""
        try:
            response = requests.get(f"{self.base_url}/bridges", auth=self.auth)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 获取桥接配置失败: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ 连接EMQX失败: {e}")
            return None
    
    def create_http_bridge(self, bridge_name="mqtt_to_mysql"):
        """创建HTTP桥接到我们的接收服务"""
        bridge_config = {
            "type": "webhook",
            "name": bridge_name,
            "enable": True,
            "url": "http://127.0.0.1:5000/mqtt/data",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json"
            },
            "body": json.dumps({
                "client_id": "${client_id}",
                "topic": "${topic}",
                "payload": "${payload}",
                "timestamp": "${timestamp}"
            }),
            "connect_timeout": "15s",
            "pool_type": "random",
            "pool_size": 8
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/bridges", 
                json=bridge_config,
                auth=self.auth
            )
            
            if response.status_code in [200, 201]:
                print(f"✅ HTTP桥接 '{bridge_name}' 创建成功")
                return True
            else:
                print(f"❌ 创建HTTP桥接失败: {response.status_code}")
                print(f"响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 创建HTTP桥接时出错: {e}")
            return False
    
    def create_rule(self, rule_name="forward_location_data", bridge_name="mqtt_to_mysql"):
        """创建规则，将包含位置信息的消息转发到桥接"""
        rule_config = {
            "id": rule_name,
            "sql": "SELECT * FROM \"location/+\" WHERE payload.latitude IS NOT NULL AND payload.longitude IS NOT NULL",
            "actions": [
                {
                    "function": "republish",
                    "args": {
                        "topic": f"$bridges/{bridge_name}",
                        "payload": "${payload}",
                        "qos": 1
                    }
                }
            ],
            "enable": True,
            "description": "转发包含地理位置信息的MQTT消息到MySQL数据库"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/rules",
                json=rule_config,
                auth=self.auth
            )
            
            if response.status_code in [200, 201]:
                print(f"✅ 规则 '{rule_name}' 创建成功")
                return True
            else:
                print(f"❌ 创建规则失败: {response.status_code}")
                print(f"响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 创建规则时出错: {e}")
            return False
    
    def test_connection(self):
        """测试EMQX连接"""
        try:
            response = requests.get(f"{self.base_url}/status", auth=self.auth)
            if response.status_code == 200:
                print("✅ EMQX连接成功")
                return True
            else:
                print(f"❌ EMQX连接失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接到EMQX: {e}")
            return False
    
    def show_status(self):
        """显示当前配置状态"""
        print(f"📊 EMQX桥接配置状态 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        if not self.test_connection():
            return
        
        # 显示桥接配置
        bridges = self.get_bridges()
        if bridges:
            print("🌉 现有桥接配置:")
            for bridge in bridges.get('data', []):
                status = "🟢 启用" if bridge.get('enable') else "🔴 禁用"
                print(f"  {bridge.get('name', 'Unknown'):20} | {status} | {bridge.get('type', 'Unknown')}")
        
        # 显示规则配置
        try:
            response = requests.get(f"{self.base_url}/rules", auth=self.auth)
            if response.status_code == 200:
                rules = response.json()
                print("\n📋 现有规则配置:")
                for rule in rules.get('data', []):
                    status = "🟢 启用" if rule.get('enable') else "🔴 禁用"
                    print(f"  {rule.get('id', 'Unknown'):20} | {status}")
        except Exception as e:
            print(f"❌ 获取规则配置失败: {e}")

def main():
    config = EMQXBridgeConfig()
    
    print("🔧 EMQX HTTP Bridge 配置工具")
    print("=" * 40)
    
    # 测试连接
    if not config.test_connection():
        print("❌ 无法连接到EMQX，请检查EMQX是否运行")
        return
    
    # 显示当前状态
    config.show_status()
    
    print("\n🔧 配置建议:")
    print("1. 确保接收服务运行在端口5000")
    print("2. 创建HTTP桥接指向 http://127.0.0.1:5000/mqtt/data")
    print("3. 创建规则转发location主题的消息")
    print("4. 测试发送位置数据到 location/test 主题")

if __name__ == "__main__":
    main()
