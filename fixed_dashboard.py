#!/usr/bin/env python3
from flask import Flask, render_template_string, jsonify
import mysql.connector
import json
from datetime import datetime
from collections import Counter

app = Flask(__name__)

mysql_config = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'emqx',
    'password': 'EmqxPass!123',
    'database': 'emqx_data'
}

@app.route('/')
def dashboard():
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <title>MQTT 实时监控面板</title>
    <meta charset="utf-8">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            padding: 20px;
        }
        .container { max-width: 1400px; margin: 0 auto; }
        
        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 20px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .status {
            display: inline-block;
            padding: 8px 16px;
            background: #2ecc71;
            color: white;
            border-radius: 20px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
            transition: color 0.3s ease;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
            font-weight: 500;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }
        
        .messages-panel, .chart-panel {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .panel-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: bold;
        }
        
        .messages-list {
            max-height: 500px;
            overflow-y: auto;
            padding: 0;
        }
        
        .message {
            padding: 20px;
            border-bottom: 1px solid #ecf0f1;
            transition: background-color 0.3s ease;
            animation: slideIn 0.5s ease;
        }
        
        .message:hover {
            background-color: #f8f9fa;
        }
        
        .message.new {
            background: linear-gradient(90deg, #2ecc71, transparent);
            animation: highlight 2s ease;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        @keyframes highlight {
            0% { background: linear-gradient(90deg, #2ecc71, transparent); }
            100% { background: transparent; }
        }
        
        .message-topic {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
            margin-bottom: 8px;
        }
        
        .message-meta {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        
        .message-payload {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            border-left: 4px solid #3498db;
            word-break: break-all;
        }
        
        .chart-container {
            padding: 20px;
        }
        
        .topic-item {
            display: flex;
            align-items: center;
            margin: 12px 0;
            padding: 12px;
            border-radius: 8px;
            transition: transform 0.2s ease;
        }
        
        .topic-item:hover {
            transform: translateX(5px);
        }
        
        .topic-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .topic-info {
            flex: 1;
        }
        
        .topic-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 4px;
        }
        
        .topic-count {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 MQTT 实时数据监控面板</h1>
            <p>实时显示 MQTT 消息流和统计数据</p>
            <div class="status" id="status">正在连接...</div>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-messages">-</div>
                <div class="stat-label">总消息数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="today-messages">-</div>
                <div class="stat-label">今日消息</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="active-topics">-</div>
                <div class="stat-label">活跃主题</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="active-clients">-</div>
                <div class="stat-label">活跃客户端</div>
            </div>
        </div>
        
        <div class="content-grid">
            <div class="messages-panel">
                <div class="panel-header">
                    📨 实时消息流
                </div>
                <div class="messages-list" id="messages-list">
                    <div style="padding: 40px; text-align: center; color: #7f8c8d;">
                        <div class="loading"></div>
                        <p style="margin-top: 15px;">正在加载消息...</p>
                    </div>
                </div>
            </div>
            
            <div class="chart-panel">
                <div class="panel-header">
                    📊 主题分布统计
                </div>
                <div class="chart-container" id="topics-chart">
                    <div style="text-align: center; padding: 40px; color: #7f8c8d;">
                        <div class="loading"></div>
                        <p style="margin-top: 15px;">正在分析数据...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let lastMessageId = 0;
        let isFirstLoad = true;
        
        function updateStats(data) {
            const today = new Date().toISOString().split('T')[0];
            const todayMessages = data.messages.filter(msg => msg.arrived.startsWith(today));
            
            document.getElementById('total-messages').textContent = data.stats.total_messages;
            document.getElementById('today-messages').textContent = data.stats.today_messages;
            document.getElementById('active-topics').textContent = data.stats.active_topics;
            document.getElementById('active-clients').textContent = data.stats.active_clients;
        }
        
        function updateTopicsChart(topicStats) {
            const chartDiv = document.getElementById('topics-chart');
            
            if (!topicStats || topicStats.length === 0) {
                chartDiv.innerHTML = '<div style="text-align: center; padding: 40px; color: #7f8c8d;"><p>暂无主题数据</p></div>';
                return;
            }
            
            let html = '';
            topicStats.forEach((item, index) => {
                const hue = (index * 137.5) % 360; // 黄金角度分布
                const color = `hsl(${hue}, 70%, 60%)`;
                const bgColor = `hsl(${hue}, 70%, 95%)`;
                
                html += `
                    <div class="topic-item" style="background-color: ${bgColor};">
                        <div class="topic-color" style="background-color: ${color};"></div>
                        <div class="topic-info">
                            <div class="topic-name">${item.topic}</div>
                            <div class="topic-count">${item.count} 条消息</div>
                        </div>
                    </div>
                `;
            });
            
            chartDiv.innerHTML = html;
        }
        
        function updateMessages(messages) {
            const messagesList = document.getElementById('messages-list');
            
            if (messages.length === 0) {
                messagesList.innerHTML = '<div style="padding: 40px; text-align: center; color: #7f8c8d;"><p>暂无消息</p></div>';
                return;
            }
            
            const html = messages.slice(0, 20).map((msg, index) => {
                const isNew = !isFirstLoad && msg.id > lastMessageId;
                const newClass = isNew ? 'new' : '';
                
                let payloadDisplay = msg.payload;
                try {
                    const parsed = JSON.parse(msg.payload);
                    payloadDisplay = JSON.stringify(parsed, null, 2);
                } catch (e) {
                    // 保持原样
                }
                
                return `
                    <div class="message ${newClass}">
                        <div class="message-topic">${msg.topic}</div>
                        <div class="message-meta">
                            客户端: ${msg.client_id} | 时间: ${msg.arrived}
                        </div>
                        <div class="message-payload">${payloadDisplay}</div>
                    </div>
                `;
            }).join('');
            
            messagesList.innerHTML = html;
            
            // 更新最后消息ID
            if (messages.length > 0) {
                lastMessageId = Math.max(...messages.map(m => m.id));
            }
        }
        
        function loadData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateStats(data);
                        updateMessages(data.messages);
                        updateTopicsChart(data.topic_stats);
                        
                        document.getElementById('status').textContent = 
                            `在线 - ${new Date().toLocaleTimeString()}`;
                        document.getElementById('status').style.background = '#2ecc71';
                        
                        isFirstLoad = false;
                    } else {
                        throw new Error(data.message || '数据加载失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('status').textContent = '连接失败';
                    document.getElementById('status').style.background = '#e74c3c';
                });
        }
        
        // 页面加载时立即加载数据
        loadData();
        
        // 每2秒自动更新
        setInterval(loadData, 2000);
    </script>
</body>
</html>
    ''')

@app.route('/api/data')
def api_data():
    try:
        conn = mysql.connector.connect(**mysql_config)
        cursor = conn.cursor(dictionary=True)
        
        # 获取最新消息
        cursor.execute("""
            SELECT id, client_id, topic, payload, arrived 
            FROM mqtt_messages 
            ORDER BY arrived DESC 
            LIMIT 50
        """)
        messages = cursor.fetchall()
        
        # 获取统计数据
        cursor.execute("SELECT COUNT(*) as total FROM mqtt_messages")
        total_messages = cursor.fetchone()['total']
        
        cursor.execute("SELECT COUNT(*) as today FROM mqtt_messages WHERE DATE(arrived) = CURDATE()")
        today_messages = cursor.fetchone()['today']
        
        cursor.execute("SELECT COUNT(DISTINCT topic) as topics FROM mqtt_messages")
        active_topics = cursor.fetchone()['topics']
        
        cursor.execute("SELECT COUNT(DISTINCT client_id) as clients FROM mqtt_messages")
        active_clients = cursor.fetchone()['clients']
        
        # 获取主题统计
        cursor.execute("""
            SELECT topic, COUNT(*) as count 
            FROM mqtt_messages 
            GROUP BY topic 
            ORDER BY count DESC 
            LIMIT 15
        """)
        topic_stats = cursor.fetchall()
        
        # 格式化时间
        for msg in messages:
            msg['arrived'] = msg['arrived'].strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.close()
        conn.close()
        
        return {
            'status': 'success',
            'messages': messages,
            'stats': {
                'total_messages': total_messages,
                'today_messages': today_messages,
                'active_topics': active_topics,
                'active_clients': active_clients
            },
            'topic_stats': topic_stats,
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            'status': 'error',
            'message': str(e)
        }

if __name__ == '__main__':
    print("🚀 启动修复版 MQTT 监控面板...")
    print("📡 访问地址: http://你的服务器IP:8080")
    app.run(host='0.0.0.0', port=8080, debug=False)
