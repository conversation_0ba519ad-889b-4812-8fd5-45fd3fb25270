#!/usr/bin/env python3
import mysql.connector
from flask import Flask, render_template, request
from flask_socketio import <PERSON>cket<PERSON>, emit
import json
from datetime import datetime, timedelta
import threading
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'mqtt-dashboard-secret'
socketio = SocketIO(app, cors_allowed_origins="*")

mysql_config = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'emqx',
    'password': 'EmqxPass!123',
    'database': 'emqx_data'
}

# 存储最新数据用于实时推送
latest_message_id = 0

@app.route('/')
def dashboard():
    return render_template('realtime_dashboard.html')

@app.route('/api/messages')
def get_messages():
    try:
        conn = mysql.connector.connect(**mysql_config)
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("""
            SELECT id, client_id, topic, payload, arrived 
            FROM mqtt_messages 
            ORDER BY arrived DESC 
            LIMIT 100
        """)
        
        messages = cursor.fetchall()
        
        for msg in messages:
            msg['arrived'] = msg['arrived'].strftime('%Y-%m-%d %H:%M:%S')
            try:
                msg['payload_json'] = json.loads(msg['payload'])
            except:
                msg['payload_json'] = None
        
        cursor.close()
        conn.close()
        
        return {'status': 'success', 'data': messages}
    except Exception as e:
        return {'status': 'error', 'message': str(e)}

@app.route('/api/chart_data')
def get_chart_data():
    try:
        conn = mysql.connector.connect(**mysql_config)
        cursor = conn.cursor()
        
        # 最近24小时每小时的消息数量
        cursor.execute("""
            SELECT 
                DATE_FORMAT(arrived, '%H:00') as hour,
                COUNT(*) as count
            FROM mqtt_messages 
            WHERE arrived >= NOW() - INTERVAL 24 HOUR
            GROUP BY DATE_FORMAT(arrived, '%Y-%m-%d %H')
            ORDER BY hour
        """)
        
        hourly_data = cursor.fetchall()
        
        # 主题分布
        cursor.execute("""
            SELECT topic, COUNT(*) as count
            FROM mqtt_messages 
            WHERE arrived >= NOW() - INTERVAL 24 HOUR
            GROUP BY topic
            ORDER BY count DESC
            LIMIT 10
        """)
        
        topic_data = cursor.fetchall()
        
        # 温度数据趋势（如果有）
        cursor.execute("""
            SELECT arrived, payload
            FROM mqtt_messages 
            WHERE topic LIKE '%temperature%' OR topic LIKE '%temp%'
            AND arrived >= NOW() - INTERVAL 2 HOUR
            ORDER BY arrived DESC
            LIMIT 50
        """)
        
        temp_data = []
        for row in cursor.fetchall():
            try:
                payload = json.loads(row[1])
                temp_value = None
                if 'temperature' in payload:
                    temp_value = payload['temperature']
                elif 'temp' in payload:
                    temp_value = payload['temp']
                elif 'value' in payload:
                    temp_value = payload['value']
                
                if temp_value is not None:
                    temp_data.append({
                        'time': row[0].strftime('%H:%M:%S'),
                        'value': float(temp_value)
                    })
            except:
                continue
        
        cursor.close()
        conn.close()
        
        return {
            'hourly': [{'hour': h[0], 'count': h[1]} for h in hourly_data],
            'topics': [{'topic': t[0], 'count': t[1]} for t in topic_data],
            'temperature': temp_data
        }
    except Exception as e:
        return {'error': str(e)}

def check_new_messages():
    """后台线程检查新消息并推送"""
    global latest_message_id
    
    while True:
        try:
            conn = mysql.connector.connect(**mysql_config)
            cursor = conn.cursor(dictionary=True)
            
            cursor.execute("""
                SELECT id, client_id, topic, payload, arrived 
                FROM mqtt_messages 
                WHERE id > %s
                ORDER BY id ASC
            """, (latest_message_id,))
            
            new_messages = cursor.fetchall()
            
            for msg in new_messages:
                msg['arrived'] = msg['arrived'].strftime('%Y-%m-%d %H:%M:%S')
                try:
                    msg['payload_json'] = json.loads(msg['payload'])
                except:
                    msg['payload_json'] = None
                
                # 推送新消息到所有连接的客户端
                socketio.emit('new_message', msg)
                latest_message_id = msg['id']
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"Error checking new messages: {e}")
        
        time.sleep(2)  # 每2秒检查一次

@socketio.on('connect')
def handle_connect():
    print('Client connected')
    emit('status', {'msg': 'Connected to MQTT Dashboard'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')

if __name__ == '__main__':
    # 启动后台线程检查新消息
    thread = threading.Thread(target=check_new_messages)
    thread.daemon = True
    thread.start()
    
    socketio.run(app, host='0.0.0.0', port=8080, debug=False)
