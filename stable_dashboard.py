#!/usr/bin/env python3
from flask import Flask, render_template_string, jsonify
import mysql.connector
import json
from datetime import datetime

app = Flask(__name__)

mysql_config = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'emqx',
    'password': 'EmqxPass!123',
    'database': 'emqx_data'
}

@app.route('/')
def dashboard():
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <title>MQTT 实时监控面板</title>
    <meta charset="utf-8">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            padding: 20px;
        }
        .container { max-width: 1400px; margin: 0 auto; }
        
        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 20px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .status {
            display: inline-block;
            padding: 8px 16px;
            background: #2ecc71;
            color: white;
            border-radius: 20px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
            transition: color 0.3s ease;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
            font-weight: 500;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }
        
        .messages-panel, .chart-panel {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .panel-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: bold;
        }
        
        .messages-list {
            max-height: 500px;
            overflow-y: auto;
            padding: 0;
        }
        
        .message {
            padding: 20px;
            border-bottom: 1px solid #ecf0f1;
            transition: background-color 0.3s ease;
            animation: slideIn 0.5s ease;
        }
        
        .message:hover {
            background-color: #f8f9fa;
        }
        
        .message.new {
            background: linear-gradient(90deg, #2ecc71, transparent);
            animation: highlight 2s ease;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        @keyframes highlight {
            0% { background: linear-gradient(90deg, #2ecc71, transparent); }
            100% { background: transparent; }
        }
        
        .message-topic {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
            margin-bottom: 8px;
        }
        
        .message-meta {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        
        .message-payload {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            border-left: 4px solid #3498db;
            word-break: break-all;
        }
        
        .chart-container {
            padding: 20px;
            text-align: center;
            color: #7f8c8d;
            font-size: 1.1em;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 MQTT 实时数据监控面板</h1>
            <p>实时显示 MQTT 消息流和统计数据</p>
            <div class="status" id="status">正在连接...</div>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-messages">-</div>
                <div class="stat-label">总消息数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="today-messages">-</div>
                <div class="stat-label">今日消息</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="active-topics">-</div>
                <div class="stat-label">活跃主题</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="active-clients">-</div>
                <div class="stat-label">活跃客户端</div>
            </div>
        </div>
        
        <div class="content-grid">
            <div class="messages-panel">
                <div class="panel-header">
                    📨 实时消息流
                </div>
                <div class="messages-list" id="messages-list">
                    <div style="padding: 40px; text-align: center; color: #7f8c8d;">
                        <div class="loading"></div>
                        <p style="margin-top: 15px;">正在加载消息...</p>
                    </div>
                </div>
            </div>
            
            <div class="chart-panel">
                <div class="panel-header">
                    📊 数据分析
                </div>
                <div class="chart-container">
                    <h3>主题分布</h3>
                    <div id="topics-chart">
                        <div class="loading"></div>
                        <p style="margin-top: 15px;">正在分析数据...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let lastMessageId = 0;
        let isFirstLoad = true;
        
        function updateStats(data) {
            const today = new Date().toISOString().split('T')[0];
            const todayMessages = data.filter(msg => msg.arrived.startsWith(today));
            const topics = new Set(data.map(msg => msg.topic));
            const clients = new Set(data.map(msg => msg.client_id));
            
            document.getElementById('total-messages').textContent = data.length;
            document.getElementById('today-messages').textContent = todayMessages.length;
            document.getElementById('active-topics').textContent = topics.size;
            document.getElementById('active-clients').textContent = clients.size;
            
            // 更新主题分布
            updateTopicsChart(topics);
        }
        
        function updateTopicsChart(topics) {
            const chartDiv = document.getElementById('topics-chart');
            if (topics.size === 0) {
                chartDiv.innerHTML = '<p>暂无数据</p>';
                return;
            }
            
            let html = '<div style="text-align: left;">';
            Array.from(topics).slice(0, 10).forEach((topic, index) => {
                const color = `hsl(${index * 36}, 70%, 60%)`;
                html += `
                    <div style="margin: 10px 0; padding: 8px; background: ${color}20; border-left: 4px solid ${color}; border-radius: 4px;">
                        <strong>${topic}</strong>
                    </div>
                `;
            });
            html += '</div>';
            chartDiv.innerHTML = html;
        }
        
        function updateMessages(messages) {
            const messagesList = document.getElementById('messages-list');
            
            if (messages.length === 0) {
                messagesList.innerHTML = '<div style="padding: 40px; text-align: center; color: #7f8c8d;"><p>暂无消息</p></div>';
                return;
            }
            
            const html = messages.slice(0, 20).map((msg, index) => {
                const isNew = !isFirstLoad && msg.id > lastMessageId;
                const newClass = isNew ? 'new' : '';
                
                let payloadDisplay = msg.payload;
                try {
                    const parsed = JSON.parse(msg.payload);
                    payloadDisplay = JSON.stringify(parsed, null, 2);
                } catch (e) {
                    // 保持原样
                }
                
                return `
                    <div class="message ${newClass}">
                        <div class="message-topic">${msg.topic}</div>
                        <div class="message-meta">
                            客户端: ${msg.client_id} | 时间: ${msg.arrived}
                        </div>
                        <div class="message-payload">${payloadDisplay}</div>
                    </div>
                `;
            }).join('');
            
            messagesList.innerHTML = html;
            
            // 更新最后消息ID
            if (messages.length > 0) {
                lastMessageId = Math.max(...messages.map(m => m.id));
            }
        }
        
        function loadData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateStats(data.messages);
                        updateMessages(data.messages);
                        
                        document.getElementById('status').textContent = 
                            `在线 - ${new Date().toLocaleTimeString()}`;
                        document.getElementById('status').style.background = '#2ecc71';
                        
                        isFirstLoad = false;
                    } else {
                        throw new Error(data.message || '数据加载失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('status').textContent = '连接失败';
                    document.getElementById('status').style.background = '#e74c3c';
                });
        }
        
        // 页面加载时立即加载数据
        loadData();
        
        // 每2秒自动更新
        setInterval(loadData, 2000);
    </script>
</body>
</html>
    ''')

@app.route('/api/data')
def api_data():
    try:
        conn = mysql.connector.connect(**mysql_config)
        cursor = conn.cursor(dictionary=True)
        
        # 获取最新消息
        cursor.execute("""
            SELECT id, client_id, topic, payload, arrived 
            FROM mqtt_messages 
            ORDER BY arrived DESC 
            LIMIT 50
        """)
        
        messages = cursor.fetchall()
        
        # 格式化时间
        for msg in messages:
            msg['arrived'] = msg['arrived'].strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.close()
        conn.close()
        
        return {
            'status': 'success',
            'messages': messages,
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            'status': 'error',
            'message': str(e)
        }

if __name__ == '__main__':
    print("🚀 启动 MQTT 监控面板...")
    print("📡 访问地址: http://你的服务器IP:8080")
    app.run(host='0.0.0.0', port=8082, debug=False)
