#!/usr/bin/env python3
"""
简化版天然气管道巡检系统 - 用于测试
"""

from flask import Flask, jsonify
import json
from datetime import datetime

app = Flask(__name__)

@app.route('/')
def index():
    return '''
<!DOCTYPE html>
<html>
<head>
    <title>天然气管道巡检系统</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>🔧 天然气管道巡检系统</h1>
    <p>服务正常运行！</p>
    <p>当前时间: <span id="time"></span></p>
    
    <h2>📱 精简数据格式示例：</h2>
    <pre>
方案1 - 精简字段名（推荐）:
{
  "lat": 39.9042,
  "lng": 116.4074,
  "t": 25.6,
  "p": 2.5,
  "g": 120,
  "h": 65,
  "ts": 1691856600
}

大小: ~80字节 (节省47%流量)
    </pre>
    
    <script>
        document.getElementById('time').textContent = new Date().toLocaleString();
        setInterval(() => {
            document.getElementById('time').textContent = new Date().toLocaleString();
        }, 1000);
    </script>
</body>
</html>
    '''

@app.route('/api/test')
def test_api():
    return jsonify({
        "status": "success",
        "message": "API正常工作",
        "timestamp": datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 启动简化版天然气管道巡检系统...")
    print("📍 访问地址: http://127.0.0.1:8083")
    app.run(host='0.0.0.0', port=8083, debug=True)
