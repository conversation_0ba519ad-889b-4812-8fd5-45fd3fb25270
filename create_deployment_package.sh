#!/bin/bash

# 创建生产环境部署包脚本
# 版本: 2.0.0
# 作者: BRIDGES

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 配置
PACKAGE_NAME="gas-pipeline-monitor-v2.0.0"
PACKAGE_DIR="./${PACKAGE_NAME}"
ARCHIVE_NAME="${PACKAGE_NAME}.tar.gz"

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 核心文件列表
CORE_FILES=(
    "gas_pipeline_inspection.py"
    "receiver_improved.py"
    "manage_services.sh"
    "clear_database.py"
    "README.md"
)

# 部署文件列表
DEPLOY_FILES=(
    "deploy_production.sh"
    "DEPLOYMENT_PACKAGE.md"
    "TECHNICAL_PAPER.md"
)

echo "🚀 创建天然气管道巡检监控系统部署包"
echo "================================================"

# 检查核心文件是否存在
print_info "检查核心文件..."
missing_files=0
for file in "${CORE_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_success "发现: $file"
    else
        print_warning "缺失: $file"
        ((missing_files++))
    fi
done

if [ $missing_files -gt 0 ]; then
    echo "❌ 缺少 $missing_files 个核心文件，无法创建部署包"
    exit 1
fi

# 创建部署包目录
print_info "创建部署包目录..."
rm -rf "$PACKAGE_DIR"
mkdir -p "$PACKAGE_DIR"

# 复制核心文件
print_info "复制核心服务文件..."
for file in "${CORE_FILES[@]}"; do
    cp "$file" "$PACKAGE_DIR/"
    print_success "复制: $file"
done

# 复制部署文件
print_info "复制部署脚本和文档..."
for file in "${DEPLOY_FILES[@]}"; do
    if [ -f "$file" ]; then
        cp "$file" "$PACKAGE_DIR/"
        print_success "复制: $file"
    else
        print_warning "跳过: $file (文件不存在)"
    fi
done

# 设置执行权限
print_info "设置文件权限..."
chmod +x "$PACKAGE_DIR/manage_services.sh"
chmod +x "$PACKAGE_DIR/deploy_production.sh"
chmod +x "$PACKAGE_DIR/clear_database.py"

# 创建部署包信息文件
print_info "创建部署包信息..."
cat > "$PACKAGE_DIR/PACKAGE_INFO.txt" << EOF
天然气管道巡检监控系统 - 生产环境部署包
================================================

版本: 2.0.0
创建时间: $(date '+%Y-%m-%d %H:%M:%S')
创建者: BRIDGES

核心服务文件:
- gas_pipeline_inspection.py  (Web监控界面 - 端口8082)
- receiver_improved.py        (MQTT接收服务 - 端口5000)
- manage_services.sh          (服务管理脚本)
- clear_database.py           (数据库清空工具)
- README.md                   (项目文档)

部署文件:
- deploy_production.sh        (一键部署脚本)
- DEPLOYMENT_PACKAGE.md       (部署说明文档)
- TECHNICAL_PAPER.md          (技术论文文档)

快速部署:
1. 上传整个部署包到目标服务器
2. 解压: tar -xzf ${ARCHIVE_NAME}
3. 进入目录: cd ${PACKAGE_NAME}
4. 执行部署: sudo ./deploy_production.sh

系统要求:
- Ubuntu 18.04+ / CentOS 7+
- Python 3.6+
- MySQL 5.7+
- Docker 20.10+
- 4GB+ 内存, 20GB+ 磁盘空间

访问地址:
- 监控系统: http://服务器IP:8082
- EMQX管理: http://服务器IP:18083 (admin/public)

技术支持: BRIDGES
EOF

# 创建快速启动脚本
print_info "创建快速启动脚本..."
cat > "$PACKAGE_DIR/quick_start.sh" << 'EOF'
#!/bin/bash

echo "🚀 天然气管道巡检监控系统 - 快速启动"
echo "================================================"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行: sudo ./quick_start.sh"
    exit 1
fi

echo "📋 部署选项:"
echo "1. 完整部署 (推荐新安装)"
echo "2. 仅启动服务 (已部署过)"
echo "3. 查看部署状态"
echo "4. 查看使用说明"

read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo "🚀 开始完整部署..."
        ./deploy_production.sh
        ;;
    2)
        echo "🔄 启动服务..."
        if [ -f "/opt/mqtt-to-mysql/manage_services.sh" ]; then
            cd /opt/mqtt-to-mysql
            ./manage_services.sh start-all
            ./manage_services.sh status
        else
            echo "❌ 系统未部署，请选择选项1进行完整部署"
        fi
        ;;
    3)
        echo "📊 查看部署状态..."
        if [ -f "/opt/mqtt-to-mysql/manage_services.sh" ]; then
            cd /opt/mqtt-to-mysql
            ./manage_services.sh status
            echo ""
            echo "🌐 访问地址:"
            echo "  监控系统: http://$(hostname -I | awk '{print $1}'):8082"
            echo "  EMQX管理: http://$(hostname -I | awk '{print $1}'):18083"
        else
            echo "❌ 系统未部署"
        fi
        ;;
    4)
        echo "📖 查看使用说明..."
        if [ -f "DEPLOYMENT_PACKAGE.md" ]; then
            less DEPLOYMENT_PACKAGE.md
        else
            cat PACKAGE_INFO.txt
        fi
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
EOF

chmod +x "$PACKAGE_DIR/quick_start.sh"

# 创建压缩包
print_info "创建压缩包..."
tar -czf "$ARCHIVE_NAME" "$PACKAGE_NAME"

# 计算文件大小和MD5
PACKAGE_SIZE=$(du -h "$ARCHIVE_NAME" | cut -f1)
PACKAGE_MD5=$(md5sum "$ARCHIVE_NAME" | cut -d' ' -f1)

# 显示完成信息
echo ""
echo "================================================"
print_success "部署包创建完成！"
echo ""
echo "📦 部署包信息:"
echo "  文件名: $ARCHIVE_NAME"
echo "  大小: $PACKAGE_SIZE"
echo "  MD5: $PACKAGE_MD5"
echo ""
echo "📁 包含文件:"
ls -la "$PACKAGE_DIR"
echo ""
echo "🚀 使用方法:"
echo "1. 上传 $ARCHIVE_NAME 到目标服务器"
echo "2. 解压: tar -xzf $ARCHIVE_NAME"
echo "3. 进入目录: cd $PACKAGE_NAME"
echo "4. 快速启动: sudo ./quick_start.sh"
echo "   或直接部署: sudo ./deploy_production.sh"
echo ""
print_success "部署包已准备就绪！"
