# 天然气管道巡检监控系统

基于 EMQX + MySQL + Flask + 百度地图的实时气体监控系统

## 🎯 系统概述

这是一个完整的天然气管道巡检监控系统，支持：
- 🌍 **实时地图监控** - 百度地图显示设备位置和气体浓度
- � **数据统计分析** - 气体浓度、温度、湿度、压力等传感器数据
- 🔔 **实时消息推送** - 新数据到达时立即更新界面
- 🏷️ **消息状态管理** - 标记、已读、删除等状态管理
- 📱 **响应式界面** - 支持PC和移动端访问

## �🔧 关键部署信息

### 📍 项目路径
- **服务器部署路径**: `/opt/mqtt-to-mysql/`
- **主要Web服务**: `gas_pipeline_inspection.py` (端口: 8082) ⭐ **核心服务**
- **MQTT接收服务**: `receiver_improved.py` (端口: 5000)
- **服务管理脚本**: `manage_services.sh`

### 🌐 网络配置
- **宿主机IP**: `**************`
- **EMQX连接器URL**: `http://**************:5000/mqtt/data` ⚠️ **必须使用宿主机IP，不能用localhost**
- **EMQX管理界面**: `http://**************:18083`
- **监控系统访问**: `http://**************:8082` 🌟 **主要访问地址**

### 🚀 服务启动命令
```bash
# 方法1: 使用服务管理脚本（推荐）
cd /opt/mqtt-to-mysql
./manage_services.sh start-all

# 方法2: 手动启动
cd /opt/mqtt-to-mysql
# 启动MQTT接收服务
nohup python3 receiver_improved.py > receiver.log 2>&1 &
# 启动Web监控界面
python3 gas_pipeline_inspection.py

# 检查服务状态
./manage_services.sh status
```

## ✨ 核心功能特性

### 🌟 **实时监控界面** (2025-08-13 最新版本)
- **实时数据刷新**: 每5秒检查新消息，有新数据立即更新界面
- **智能状态管理**: 消息标记、已读、删除状态持久化存储
- **响应式设计**: 支持PC和移动端，自适应屏幕尺寸
- **数据筛选**: 支持时间范围、气体浓度、设备类型等多维度筛选
- **地图集成**: 高德地图显示设备实时位置和传感器数据

### 🔬 **多气体检测支持** (2025-08-13 新增)
- **常用工业气体**: 甲烷(CH4)、氢气(H2)、一氧化碳(CO)、二氧化碳(CO2)
- **有毒有害气体**: 硫化氢(H2S)、氨气(NH3)、二氧化硫(SO2)、二氧化氮(NO2)
- **特殊气体**: 六氟化硫(SF6)、臭氧(O3)、挥发性有机物(VOC)
- **烷烃类气体**: 乙烷(C2H6)、丙烷(C3H8)、丁烷(C4H10)
- **智能显示**: 自动识别气体类型，显示中文名称和对应单位
- **危险阈值**: 根据不同气体类型设置专业的警告和危险阈值

### �️ **多气体地图标记** (2025-08-13 最新)
- **全气体显示**: 地图标点显示所有检测到的气体浓度值
- **智能布局**: 1个气体显示单行，2-3个气体分行显示，超过3个显示"+N种"
- **危险优先**: 按危险程度排序显示，最危险的气体优先展示
- **颜色预警**: 根据最高危险级别确定标记颜色(绿/黄/红)
- **自适应大小**: 标记大小根据气体数量自动调整

### �🔧 **最近重大更新** (2025-08-13)
1. **JSON格式验证和ID计数修复** ✅ **最新**
   - 修复了无效JSON导致数据库ID递增但数据丢失的问题
   - 增强了数据验证机制，确保只有有效数据才会被存储
   - 解决了清空数据后显示错误总数的问题
   - 添加了完整的事务处理，确保数据一致性

2. **EMQX数据丢弃问题修复** ✅
   - 修复了EMQX连接器URL配置问题（必须使用宿主机IP）
   - 解决了数据接收但不显示的问题
   - 优化了数据库连接配置

3. **实时刷新系统优化** ✅
   - 双重刷新机制：5秒实时检查 + 30秒定时刷新
   - 新消息通知提示
   - 实时开关控制
   - 调试模式查看消息状态

4. **消息状态管理完善** ✅
   - 修复标记/取消标记逻辑
   - 状态持久化存储（localStorage）
   - 自动清理过期状态（7天）

### 🔧 调试实时刷新
如果实时刷新不工作，请按以下步骤调试：

1. **启动巡检系统**:
```bash
cd /opt/mqtt-to-mysql
python3 gas_pipeline_inspection.py
```

2. **打开浏览器控制台** (F12)，查看JavaScript日志

3. **点击"调试"按钮**，查看消息ID状态

4. **运行测试脚本**:
```bash
python3 test_realtime.py
```

5. **检查日志输出**，确认：
   - lastMessageId是否正确初始化
   - API是否返回新消息
   - 实时检查是否正常运行

## 🏗️ 系统架构

```
MQTT设备 → EMQX Broker → HTTP Bridge → 接收服务 → MySQL → Web界面
    ↓           ↓              ↓            ↓         ↓        ↓
  传感器数据   消息路由      HTTP转发     数据解析   数据存储   实时显示
```

### 🔧 服务组件

1. **EMQX MQTT Broker** (端口: 1883, 18083)
   - MQTT消息代理和路由
   - HTTP Bridge转发消息到接收服务
   - 管理界面: `http://**************:18083`

2. **MQTT接收服务** (端口: 5000) - `receiver_improved.py`
   - 接收EMQX转发的HTTP请求
   - 解析传感器数据（气体、温度、湿度、压力等）
   - 存储到MySQL数据库
   - 健康检查: `http://**************:5000/health`

3. **Web监控界面** (端口: 8082) - `gas_pipeline_inspection.py` ⭐
   - 实时数据展示和地图显示
   - 消息状态管理（标记、删除等）
   - 数据筛选和统计分析
   - 访问地址: `http://**************:8082`

4. **MySQL数据库** (端口: 3306)
   - 数据库: `emqx_data`
   - 主表: `mqtt_messages`
   - 用户: `emqx` / 密码: `EmqxPass!123`

## 🚀 快速启动

### 🎯 一键启动（推荐）
```bash
cd /opt/mqtt-to-mysql
./manage_services.sh start-all
```

### 📋 分步启动
```bash
# 1. 启动MQTT接收服务
./manage_services.sh start-mqtt

# 2. 启动Web监控界面
./manage_services.sh start-web

# 3. 检查服务状态
./manage_services.sh status
```

### 🔍 服务管理
```bash
# 查看所有可用命令
./manage_services.sh help

# 重启所有服务
./manage_services.sh restart-all

# 查看日志
./manage_services.sh logs-web
./manage_services.sh logs-mqtt

# 停止服务
./manage_services.sh stop-all
```

## 🌐 系统访问地址

- **🌟 监控系统主界面**: http://**************:8082 ⭐ **主要访问地址**
- **EMQX管理界面**: http://**************:18083 (admin/public)
- **接收服务健康检查**: http://**************:5000/health

## 📡 MQTT 数据格式

### 📍 位置和传感器数据
发送到主题 `location/{device_id}`:

```json
{
    "lat": 39.9042,           // 纬度 (必需)
    "lng": 116.4074,          // 经度 (必需)

    // 基础环境数据
    "temp": 28.0,             // 温度 (°C)
    "humidity": 70,           // 湿度 (%)
    "pressure": 1012.8,       // 压力 (MPa)

    // 气体检测数据 (支持多种气体同时检测)
    "CH4": 2500,              // 甲烷 (ppm)
    "H2": 23730,              // 氢气 (ppm) ⭐ 您的格式
    "CO": 15,                 // 一氧化碳 (ppm)
    "H2S": 8,                 // 硫化氢 (ppm)
    "SF6": 1200,              // 六氟化硫 (ppm) ⭐ 新增支持
    "CO2": 800,               // 二氧化碳 (ppm)
    "NH3": 25,                // 氨气 (ppm)
    "SO2": 5,                 // 二氧化硫 (ppm)
    "NO2": 3,                 // 二氧化氮 (ppm)
    "O2": 20.9,               // 氧气 (%)
    "VOC": 300,               // 挥发性有机物 (ppm)

    // 设备信息
    "device_id": "device_001", // 设备ID
    "name": "巡检点A",         // 设备名称
    "timestamp": "2025-08-13T20:30:00"
}
```

### 🔧 支持的坐标字段
- 纬度: `lat`, `latitude`
- 经度: `lng`, `lon`, `longitude`

### 📊 支持的气体类型
| 气体名称 | 字段名 | 单位 | 危险阈值 | 说明 |
|----------|--------|------|----------|------|
| 甲烷 | `CH4`, `ch4` | ppm | >5000 | 天然气主要成分 |
| 氢气 | `H2`, `h2` | ppm | >4000 | 易燃易爆气体 |
| 一氧化碳 | `CO`, `co` | ppm | >100 | 有毒气体 |
| 硫化氢 | `H2S`, `h2s` | ppm | >20 | 剧毒气体 |
| 六氟化硫 | `SF6`, `sf6` | ppm | >5000 | 电力设备绝缘气体 |
| 二氧化碳 | `CO2`, `co2` | ppm | >10000 | 窒息性气体 |
| 氨气 | `NH3`, `nh3` | ppm | >50 | 刺激性气体 |
| 二氧化硫 | `SO2`, `so2` | ppm | >10 | 有毒气体 |
| 二氧化氮 | `NO2`, `no2` | ppm | >5 | 有毒气体 |
| 氧气 | `O2`, `o2` | % | <16 | 缺氧危险 |
| 臭氧 | `O3`, `o3` | ppm | >0.2 | 强氧化性 |
| 挥发性有机物 | `VOC`, `voc` | ppm | >1000 | 有机污染物 |

### 🌡️ 环境传感器数据
- `temp`, `temperature`, `t` - 温度 (°C)
- `humidity`, `hum`, `h` - 湿度 (%)
- `pressure`, `press`, `p` - 压力 (MPa)
- `pm25` - PM2.5 (μg/m³)
- `pm10` - PM10 (μg/m³)
- `noise` - 噪声 (dB)
- `light` - 光照 (lux)
- `uv` - 紫外线 (mW/cm²)

## 🔧 配置说明

### ⚠️ EMQX HTTP Bridge 关键配置

#### 1. 连接器配置 (mysql-http-bridge)
- **名称**: `mysql-http-bridge`
- **URL**: `http://**************:5000/mqtt/data` ⚠️ **必须使用宿主机IP**
- **方法**: `POST`
- **请求头**: `Content-Type: application/json`

#### 2. 规则配置 (mqtt-mysql)
- **名称**: `mqtt-mysql`
- **SQL**: `SELECT clientid as client_id, topic, payload, qos, retain, timestamp as arrived FROM "location/+"`
- **动作**: 转发到 mysql-http-bridge 连接器

#### 3. 请求体模板
```json
{
  "client_id": "${client_id}",
  "topic": "${topic}",
  "payload": "${payload}",
  "qos": ${qos},
  "retain": ${retain},
  "timestamp": "${timestamp}"
}
```

#### 4. 配置步骤
1. 访问EMQX管理界面: `http://**************:18083`
2. 用户名: `admin`, 密码: `public`
3. 进入 **集成** → **连接器** → 编辑 `mysql-http-bridge`
4. 修改URL为宿主机IP: `http://**************:5000/mqtt/data`
5. 点击 **"测试连接"** 确保成功
6. 点击 **"更新"** 保存配置

### MySQL配置
```python
mysql_config = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'emqx',
    'password': 'EmqxPass!123',
    'database': 'emqx_data'
}
```

### 百度地图API
- API Key: `0tb94kg40u0eN1PKDUHkvVQTjZmfus8Z`
- 支持的坐标字段: `latitude/lat`, `longitude/lng/lon`

## 🛠️ 开发部署

### 本地开发
1. 使用VSCode + SFTP插件
2. 配置文件: `.vscode/sftp.json`
3. 自动同步: 保存时自动上传到服务器

### 服务管理命令
```bash
# 查看所有可用命令
python3 service_manager.py

# 启动特定服务
python3 service_manager.py start receiver
python3 service_manager.py start map

# 停止服务
python3 service_manager.py stop receiver
python3 service_manager.py stop-all

# 查看状态
python3 service_manager.py status
```
# 后台启动接收服务
cd /opt/mqtt-to-mysql
nohup python3 receiver_improved.py > receiver.log 2>&1 &

# 启动地图服务
python3 baidu_map_fixed.py
## 🧪 系统测试

### 🔬 多气体检测功能测试 (推荐)
```bash
# 使用专用多气体检测测试脚本
python3 test_gas_detection.py

# 测试包含15+种气体类型：
# 易燃气体: CH4(甲烷), H2(氢气), C2H6(乙烷), C3H8(丙烷), C4H10(丁烷)
# 有毒气体: CO(一氧化碳), H2S(硫化氢), NH3(氨气), SO2(二氧化硫), NO2(二氧化氮)
# 工业气体: SF6(六氟化硫), CO2(二氧化碳), O2(氧气), O3(臭氧), VOC(挥发性有机物)
```

### �️ 多气体地图标记显示测试 (最新)
```bash
# 使用多气体地图标记显示测试脚本
python3 test_multi_gas_display.py

# 测试6种不同场景：
# 1. 单气体显示 - 显示1个气体数值
# 2. 双气体显示 - 显示2个气体数值
# 3. 三气体显示 - 显示3个气体数值
# 4. 多气体显示 - 显示前3个 + "+N种"提示
# 5. 危险级别 - 红色标记预警
# 6. 混合浓度 - 不同危险级别混合
```

### �📡 手动测试数据发送
```bash
# 基础多气体检测测试
mosquitto_pub -h ************** -t "location/multi_gas_test" -m '{
  "lat": 39.920,
  "lng": 116.410,
  "H2": 23730,
  "SF6": 1200,
  "CH4": 2500,
  "CO": 15,
  "temp": 28.0,
  "humidity": 70,
  "pressure": 1012.8,
  "device_id": "multi_gas_test",
  "name": "多气体检测设备"
}'

# 危险级别预警测试
mosquitto_pub -h ************** -t "location/danger_test" -m '{
  "lat": 39.925,
  "lng": 116.415,
  "H2": 45000,
  "CO": 150,
  "H2S": 35,
  "CH4": 8000,
  "device_id": "danger_test",
  "name": "危险级别测试设备"
}'

# 传统测试数据（兼容）
mosquitto_pub -h ************** -t "location/basic_test" -m '{
  "lat": 39.9042,
  "lng": 116.4074,
  "gas": 2500,
  "temp": 25.0,
  "device_id": "basic_test",
  "name": "基础测试设备"
}'
```

### 验证数据流

#### 🧪 完整测试流程
```bash
# 1. 确保接收服务运行
curl http://**************:5000/health

# 2. 发送测试MQTT消息
mosquitto_pub -h localhost -t "location/device_001" \
  -m '{"lat":39.920,"lng":116.410,"gas":2500,"temp":28.0,"humidity":70,"pressure":1012.8}'

# 3. 检查数据库记录
mysql -u emqx -pEmqxPass!123 emqx_data -e "SELECT * FROM mqtt_messages ORDER BY id DESC LIMIT 5;"

# 4. 访问Web界面
# 地图服务: http://**************:8081
# 监控面板: http://**************:8082
```

#### 📊 预期结果
1. EMQX规则统计显示消息已处理
2. 接收服务日志显示收到HTTP请求
3. MySQL表中有新记录
4. 地图界面显示设备位置

## 📁 核心文件说明

### 🌟 **生产环境文件**
- `gas_pipeline_inspection.py` - **主要Web监控界面** (端口8082) ⭐
- `receiver_improved.py` - **MQTT数据接收服务** (端口5000) ⭐
- `manage_services.sh` - **服务管理脚本** ⭐
- `clear_database.py` - **数据库清空工具**

### 🔧 **管理和测试工具**
- `test_json_validation.py` - **JSON格式验证和ID计数测试脚本** ⭐ 最新
- `quick_count_test.py` - **快速计数修复验证脚本** ⭐ 最新
- `test_gas_detection.py` - **多气体检测功能测试脚本** ⭐ 新增
- `test_multi_gas_display.py` - **多气体地图标记显示测试脚本** ⭐ 新增
- `test_data_flow.py` - 数据流测试脚本
- `check_server_status.py` - 服务器状态检查
- `check_emqx_bridge.py` - EMQX桥接状态检查
- `emqx_bridge_config.py` - EMQX桥接配置工具
- `generate_hebei_data.py` - 河北地区测试数据生成器

### 📦 **备份和开发文件**
- `baidu_map_fixed.py` - 旧版地图服务（已被gas_pipeline_inspection.py替代）
- `receiver_*.py` - 各种版本的接收器（保留作为备份）
- `*_backup.py`, `*_debug.py` - 开发过程中的备份文件

## 🔍 故障排除

### ⚠️ **常见问题及解决方案**

#### 1. **数据不显示或EMQX数据丢弃**
```bash
# 检查EMQX连接器URL配置（最常见问题）
# 必须使用宿主机IP，不能用localhost
curl http://**************:5000/health

# 检查EMQX管理界面中的连接器配置
# URL应该是: http://**************:5000/mqtt/data
```

#### 2. **服务启动失败**
```bash
# 检查端口占用
lsof -i :8082  # Web服务端口
lsof -i :5000  # MQTT接收服务端口

# 强制停止冲突进程
pkill -9 -f gas_pipeline_inspection
pkill -9 -f receiver_improved

# 重新启动
./manage_services.sh restart-all0
```

#### 3. **实时刷新不工作**
```bash
# 检查浏览器控制台（F12）
# 点击页面上的"调试"按钮查看消息ID状态
# 确认lastMessageId是否正确初始化
```

#### 4. **数据库连接问题**
```bash
# 测试数据库连接
mysql -u emqx -pEmqxPass!123 emqx_data -e "SELECT COUNT(*) FROM mqtt_messages;"

# 清空数据库重新开始
python3 clear_database.py
```

### 📋 **诊断命令**
```bash
# 查看服务状态
./manage_services.sh status

# 查看日志
./manage_services.sh logs-web    # Web服务日志
./manage_services.sh logs-mqtt   # MQTT服务日志

# 查看进程
ps aux | grep -E "(gas_pipeline|receiver)" | grep -v grep

# 查看端口占用
netstat -tlnp | grep -E "(5000|8082|1883|3306)"

# 测试数据流
python3 test_data_flow.py
```

## 📊 系统监控指标

- **实时数据**: MQTT消息接收数量和频率
- **存储状态**: 数据库记录数和存储状态
- **界面响应**: 地图标记显示和页面响应时间
- **服务健康**: 各服务运行状态和资源使用

## 🎯 **数据库管理**

```bash
# 清空数据库（重新开始）
python3 clear_database.py

# 查看数据统计
mysql -u emqx -pEmqxPass!123 emqx_data -e "
SELECT
    COUNT(*) as total_messages,
    MIN(arrived) as first_message,
    MAX(arrived) as last_message
FROM mqtt_messages;"
```

## 🔬 多气体检测功能详解

### 支持的气体类型
| 类别 | 气体 | 字段名 | 中文显示 | 单位 | 危险阈值 |
|------|------|--------|----------|------|----------|
| 易燃气体 | 甲烷 | `CH4` | 甲烷 | ppm | >5000 |
| | 氢气 | `H2` | 氢气 | ppm | >4000 |
| | 乙烷 | `C2H6` | 乙烷 | ppm | >3000 |
| 有毒气体 | 一氧化碳 | `CO` | 一氧化碳 | ppm | >100 |
| | 硫化氢 | `H2S` | 硫化氢 | ppm | >20 |
| | 氨气 | `NH3` | 氨气 | ppm | >50 |
| 工业气体 | 六氟化硫 | `SF6` | 六氟化硫 | ppm | >5000 |
| | 二氧化碳 | `CO2` | 二氧化碳 | ppm | >10000 |
| | 挥发性有机物 | `VOC` | 挥发性有机物 | ppm | >1000 |

### 智能显示特性
- 🇨🇳 **中文名称**: 自动识别气体类型并显示中文名称
- 📊 **专业单位**: 根据气体类型自动显示正确单位(ppm, %, μg/m³等)
- 🎨 **颜色预警**: 绿色(安全)、黄色(警告)、红色(危险)
- 📋 **分类显示**: 气体检测数据与环境数据分类显示
- ⚠️ **专业阈值**: 基于工业安全标准的危险等级判断

### 使用示例
```json
// 您的氢气数据格式 ✅
{
    "H2": 23730,              // 显示为: 氢气: 23730 ppm [红色-危险]
    "SF6": 1200,              // 显示为: 六氟化硫: 1200 ppm [黄色-警告]
    "CH4": 500                // 显示为: 甲烷: 500 ppm [绿色-正常]
}
```

---

**项目**: 天然气管道巡检监控系统
**开发者**: BRIDGES
**最后更新**: 2025-08-13
**版本**: 2.0.0 - 多气体地图标记版本 ⭐
**部署环境**: 阿里云ECS + EMQX + MySQL + Flask + 高德地图
**核心功能**: 15+种工业气体检测 + 多气体地图标记显示
