#!/usr/bin/env python3
"""
一键部署脚本
自动启动所有服务并验证系统状态
"""

import subprocess
import time
import sys
import requests
from datetime import datetime

def run_command(cmd, description=""):
    """执行命令并显示结果"""
    if description:
        print(f"🔧 {description}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            if result.stdout.strip():
                print(f"✅ {result.stdout.strip()}")
            return True
        else:
            print(f"❌ 命令执行失败: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ 执行出错: {e}")
        return False

def check_service(url, service_name, timeout=5):
    """检查服务是否运行"""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            print(f"✅ {service_name} 运行正常")
            return True
        else:
            print(f"❌ {service_name} 响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {service_name} 连接失败: {e}")
        return False

def deploy():
    """执行部署"""
    print("🚀 MQTT地理位置监控系统 - 一键部署")
    print("=" * 50)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 步骤1: 停止现有服务
    print("🛑 步骤1: 停止现有服务")
    run_command("pkill -f 'python.*baidu_map'", "停止地图服务")
    run_command("pkill -f 'python.*receiver'", "停止接收服务")
    run_command("pkill -f 'python.*stable_dashboard'", "停止稳定版服务")
    time.sleep(2)
    
    # 步骤2: 检查依赖
    print("\n🔍 步骤2: 检查系统依赖")
    dependencies = [
        ("python3 --version", "Python版本"),
        ("pip3 --version", "pip版本"),
        ("docker ps", "Docker状态")
    ]
    
    for cmd, desc in dependencies:
        run_command(cmd, desc)
    
    # 步骤3: 安装Python依赖
    print("\n📦 步骤3: 安装Python依赖")
    required_packages = [
        "flask",
        "mysql-connector-python", 
        "paho-mqtt",
        "requests"
    ]
    
    for package in required_packages:
        run_command(f"pip3 install {package}", f"安装 {package}")
    
    # 步骤4: 启动服务
    print("\n🚀 步骤4: 启动服务")
    
    # 启动接收服务 (后台运行)
    print("启动MQTT接收服务...")
    subprocess.Popen([
        'nohup', 'python3', 'receiver_fixed.py'
    ], stdout=open('/dev/null', 'w'), stderr=open('/dev/null', 'w'))
    time.sleep(3)
    
    # 启动地图服务 (后台运行)
    print("启动百度地图服务...")
    subprocess.Popen([
        'nohup', 'python3', 'baidu_map_fixed.py'
    ], stdout=open('/dev/null', 'w'), stderr=open('/dev/null', 'w'))
    time.sleep(3)
    
    # 步骤5: 验证服务
    print("\n✅ 步骤5: 验证服务状态")
    services_to_check = [
        ("http://127.0.0.1:5000/health", "MQTT接收服务"),
        ("http://127.0.0.1:8081", "百度地图服务"),
        ("http://127.0.0.1:18083", "EMQX管理界面")
    ]
    
    all_ok = True
    for url, name in services_to_check:
        if not check_service(url, name):
            all_ok = False
    
    # 步骤6: 运行测试
    print("\n🧪 步骤6: 运行数据流测试")
    if run_command("python3 test_data_flow.py", "执行完整数据流测试"):
        print("✅ 数据流测试完成")
    
    # 部署结果
    print("\n" + "=" * 50)
    print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if all_ok:
        print("🎉 部署成功！")
        print()
        print("📱 访问地址:")
        print("  🗺️ 地图监控: http://101.200.72.188:8081")
        print("  ⚙️ EMQX管理: http://101.200.72.188:18083")
        print()
        print("🧪 测试MQTT发布:")
        print('  mosquitto_pub -h 127.0.0.1 -t "location/test" -m \'{"latitude":39.9042,"longitude":116.4074,"name":"测试位置"}\'')
        print()
        print("📊 服务管理:")
        print("  python3 service_manager.py status")
        print("  python3 service_manager.py stop-all")
    else:
        print("⚠️ 部署完成，但部分服务可能有问题")
        print("请检查服务日志并手动排查")

def show_help():
    """显示帮助信息"""
    print("🚀 MQTT地理位置监控系统部署工具")
    print()
    print("用法:")
    print("  python3 deploy.py          # 执行完整部署")
    print("  python3 deploy.py --help   # 显示此帮助")
    print()
    print("部署步骤:")
    print("1. 停止现有服务")
    print("2. 检查系统依赖")
    print("3. 安装Python依赖")
    print("4. 启动核心服务")
    print("5. 验证服务状态")
    print("6. 运行数据流测试")

def main():
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        show_help()
        return
    
    deploy()

if __name__ == "__main__":
    main()
