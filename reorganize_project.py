#!/usr/bin/env python3
"""
项目重组脚本 - v1.0
整理项目文件到合理的目录结构
"""

import os
import shutil
from datetime import datetime

class ProjectReorganizer:
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 定义目录结构
        self.directories = {
            'core': '核心服务文件',
            'tools': '开发和管理工具',
            'tests': '测试脚本',
            'backup': '备份和旧版本文件',
            'docs': '文档文件',
            'templates': 'HTML模板文件',
            'config': '配置文件'
        }
        
        # 文件分类
        self.file_mapping = {
            # 核心服务文件 (必须在根目录)
            'core': [
                'baidu_map_fixed.py',      # 主要地图服务 - v1.0
                'receiver_improved.py',    # 改进的接收服务 - v1.0
            ],
            
            # 开发和管理工具
            'tools': [
                'service_manager.py',      # 服务管理工具 - v1.0
                'deploy.py',              # 一键部署脚本 - v1.0
                'quick_fix.py',           # 快速修复工具 - v1.0
                'cleanup_project.py',     # 项目清理工具 - v1.0
                'reorganize_project.py',  # 本脚本 - v1.0
                'emqx_bridge_config.py',  # EMQX配置工具 - v1.0
            ],
            
            # 测试脚本
            'tests': [
                'test_data_flow.py',      # 数据流测试 - v1.0
                'check_server_status.py', # 状态检查 - v1.0
                'check_emqx_bridge.py',   # EMQX桥接检查 - v1.0
                'check_mysql_schema.py',  # 数据库结构检查 - v1.0
            ],
            
            # 备份文件 (旧版本)
            'backup': [
                'map_dashboard_backup.py',
                'receiver.py',
                'receiver_debug.py',
                'receiver_simple.py',
                'receiver_fixed.py',      # 旧版接收服务
                'mqtt_final.py',
                'mqtt_parser.py',
                'realtime_dashboard.py',
                'realtime_fixed.py',
                'simple.py',
                'fixed_dashboard.py',
                'map_dashboard.py',
                'stable_dashboard.py',
            ],
            
            # 文档文件
            'docs': [
                'README.md',
                'PROJECT_INFO.md',
            ],
            
            # 配置文件
            'config': [
                'run_remote_check.bat',
            ]
        }
    
    def create_directories(self):
        """创建目录结构"""
        print("📁 创建目录结构...")
        
        for dir_name, description in self.directories.items():
            if not os.path.exists(dir_name):
                os.makedirs(dir_name)
                print(f"✅ 创建目录: {dir_name}/ - {description}")
            else:
                print(f"📁 目录已存在: {dir_name}/")
    
    def move_files(self):
        """移动文件到对应目录"""
        print("\n📦 移动文件...")
        moved_count = 0
        
        for category, files in self.file_mapping.items():
            if category == 'core':
                # 核心文件保留在根目录，只添加版本注释
                for filename in files:
                    if os.path.exists(filename):
                        self.add_version_comment(filename)
                        print(f"✅ 核心文件保留: {filename}")
                continue
            
            for filename in files:
                if os.path.exists(filename):
                    try:
                        # 移动文件
                        dest_path = os.path.join(category, filename)
                        shutil.move(filename, dest_path)
                        
                        # 添加版本注释
                        self.add_version_comment(dest_path)
                        
                        print(f"📦 移动: {filename} → {category}/{filename}")
                        moved_count += 1
                        
                    except Exception as e:
                        print(f"❌ 移动失败 {filename}: {e}")
                else:
                    print(f"⚠️ 文件不存在: {filename}")
        
        print(f"\n✅ 成功移动 {moved_count} 个文件")
    
    def add_version_comment(self, filepath):
        """为Python文件添加版本注释"""
        if not filepath.endswith('.py'):
            return
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已有版本注释
            if '# Version:' in content or '版本:' in content:
                return
            
            # 在第一行shebang后添加版本信息
            lines = content.split('\n')
            insert_index = 0
            
            # 找到合适的插入位置
            for i, line in enumerate(lines):
                if line.startswith('#!'):
                    insert_index = i + 1
                elif line.startswith('"""') or line.startswith("'''"):
                    # 在文档字符串后插入
                    for j in range(i + 1, len(lines)):
                        if lines[j].endswith('"""') or lines[j].endswith("'''"):
                            insert_index = j + 1
                            break
                    break
                elif line.strip() and not line.startswith('#'):
                    break
            
            # 插入版本信息
            version_comment = f"# Version: 1.0 | Created: {self.timestamp} | Status: Active"
            lines.insert(insert_index, version_comment)
            
            # 写回文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
                
        except Exception as e:
            print(f"⚠️ 添加版本注释失败 {filepath}: {e}")
    
    def create_directory_readme(self):
        """为每个目录创建说明文件"""
        print("\n📝 创建目录说明文件...")
        
        readme_contents = {
            'core': """# 核心服务文件

这个目录包含系统的核心服务文件，必须保留在根目录以便正常运行。

## 文件说明
- `baidu_map_fixed.py` - 百度地图显示服务 (端口: 8081)
- `receiver_improved.py` - MQTT数据接收服务 (端口: 5000)

## 注意事项
- 这些文件不能移动到子目录
- 修改后需要重启对应服务
- 通过SFTP自动同步到服务器
""",
            
            'tools': """# 开发和管理工具

这个目录包含项目开发、部署和管理相关的工具脚本。

## 文件说明
- `service_manager.py` - 服务启停管理工具
- `deploy.py` - 一键部署脚本
- `quick_fix.py` - 快速问题修复工具
- `emqx_bridge_config.py` - EMQX桥接配置工具

## 使用方法
```bash
cd /opt/mqtt-to-mysql
python3 tools/service_manager.py start-all
python3 tools/deploy.py
```
""",
            
            'tests': """# 测试脚本

这个目录包含各种测试和检查脚本。

## 文件说明
- `test_data_flow.py` - 完整数据流测试
- `check_server_status.py` - 服务器状态检查
- `check_emqx_bridge.py` - EMQX桥接状态检查
- `check_mysql_schema.py` - 数据库结构检查

## 使用方法
```bash
python3 tests/test_data_flow.py
python3 tests/check_server_status.py
```
""",
            
            'backup': """# 备份和旧版本文件

这个目录包含项目开发过程中的旧版本文件和备份。

## 注意事项
- 这些文件可以安全删除
- 保留作为参考或回滚使用
- 定期清理以节省空间

## 清理建议
如果系统运行稳定，可以删除以下类型的文件：
- `*_backup.py` - 备份文件
- `*_debug.py` - 调试版本
- `*_simple.py` - 简化版本
""",
            
            'docs': """# 项目文档

这个目录包含项目相关的文档文件。

## 文件说明
- `README.md` - 项目主要文档
- `PROJECT_INFO.md` - 项目信息和配置说明

## 维护
- 随着项目更新及时更新文档
- 记录重要的配置变更
""",
            
            'config': """# 配置文件

这个目录包含项目的配置文件。

## 文件说明
- 各种环境配置文件
- 部署脚本
- 系统配置

## 注意事项
- 配置文件包含敏感信息，注意安全
- 修改配置后需要重启相关服务
"""
        }
        
        for dir_name, content in readme_contents.items():
            if os.path.exists(dir_name):
                readme_path = os.path.join(dir_name, 'README.md')
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"📝 创建说明: {dir_name}/README.md")
    
    def update_sftp_ignore(self):
        """更新SFTP忽略配置"""
        sftp_config_path = '.vscode/sftp.json'
        if os.path.exists(sftp_config_path):
            try:
                with open(sftp_config_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 添加新的忽略规则
                new_ignores = [
                    '"backup/**"',
                    '"tests/**"',
                    '"tools/**"',
                    '"docs/**"',
                    '"config/**"'
                ]
                
                # 简单的字符串替换来添加忽略规则
                if '"backup_files/**"' in content:
                    content = content.replace(
                        '"backup_files/**"',
                        '"backup_files/**",\n        ' + ',\n        '.join(new_ignores)
                    )
                    
                    with open(sftp_config_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print("✅ 更新SFTP忽略配置")
                
            except Exception as e:
                print(f"⚠️ 更新SFTP配置失败: {e}")
    
    def show_new_structure(self):
        """显示新的项目结构"""
        print("\n📁 重组后的项目结构:")
        print("=" * 40)
        print("mqtt-location-dashboard/")
        print("├── baidu_map_fixed.py          # 主要地图服务 - v1.0")
        print("├── receiver_improved.py        # 接收服务 - v1.0")
        print("├── templates/                  # HTML模板")
        print("├── .vscode/                    # VSCode配置")
        print("├── core/                       # 核心服务 (符号链接)")
        print("├── tools/                      # 开发工具")
        print("│   ├── service_manager.py")
        print("│   ├── deploy.py")
        print("│   ├── quick_fix.py")
        print("│   └── README.md")
        print("├── tests/                      # 测试脚本")
        print("│   ├── test_data_flow.py")
        print("│   ├── check_server_status.py")
        print("│   └── README.md")
        print("├── backup/                     # 旧版本文件")
        print("│   ├── map_dashboard_backup.py")
        print("│   ├── receiver_*.py")
        print("│   └── README.md")
        print("├── docs/                       # 文档")
        print("│   └── README.md")
        print("└── config/                     # 配置文件")
        print("    └── README.md")
    
    def reorganize(self):
        """执行重组"""
        print("🗂️ 项目文件重组工具 - v1.0")
        print("=" * 50)
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 1. 创建目录
        self.create_directories()
        
        # 2. 移动文件
        self.move_files()
        
        # 3. 创建说明文件
        self.create_directory_readme()
        
        # 4. 更新SFTP配置
        self.update_sftp_ignore()
        
        # 5. 显示新结构
        self.show_new_structure()
        
        print("\n" + "=" * 50)
        print("✅ 项目重组完成！")
        print()
        print("💡 使用说明:")
        print("  🚀 启动服务: python3 tools/service_manager.py start-all")
        print("  🧪 运行测试: python3 tests/test_data_flow.py")
        print("  🔧 快速修复: python3 tools/quick_fix.py")
        print("  📚 查看文档: cat docs/README.md")
        print()
        print("🗑️ 清理建议:")
        print("  - backup/ 目录中的文件可以安全删除")
        print("  - 系统稳定后可以删除 tests/ 中的临时测试文件")

def main():
    reorganizer = ProjectReorganizer()
    
    print("🗂️ 项目文件重组工具")
    print("=" * 30)
    print("此工具将:")
    print("1. 创建合理的目录结构")
    print("2. 移动文件到对应目录")
    print("3. 为每个目录添加说明文档")
    print("4. 更新SFTP忽略配置")
    print("5. 为所有Python文件添加版本注释")
    print()
    print("⚠️ 注意: 核心服务文件将保留在根目录")
    print()
    
    response = input("是否继续重组项目? (y/N): ").lower().strip()
    
    if response in ['y', 'yes']:
        reorganizer.reorganize()
    else:
        print("❌ 重组已取消")

if __name__ == "__main__":
    main()
