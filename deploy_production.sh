#!/bin/bash

# 天然气管道巡检监控系统 - 一键部署脚本
# 版本: 2.0.0
# 作者: BRIDGES
# 日期: 2025-08-13

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="天然气管道巡检监控系统"
DEPLOY_DIR="/opt/mqtt-to-mysql"
BACKUP_DIR="/opt/mqtt-backup-$(date +%Y%m%d_%H%M%S)"
MYSQL_USER="emqx"
MYSQL_PASSWORD="EmqxPass!123"
MYSQL_DATABASE="emqx_data"
HOST_IP="**************"

# 打印带颜色的消息
print_header() {
    echo -e "${PURPLE}================================================${NC}"
    echo -e "${PURPLE}🚀 $1${NC}"
    echo -e "${PURPLE}================================================${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查系统要求
check_requirements() {
    print_header "检查系统要求"
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        print_error "请使用root用户运行此脚本"
        exit 1
    fi
    
    # 检查Python3
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装，正在安装..."
        apt update && apt install -y python3 python3-pip
    fi
    print_success "Python3 已安装"
    
    # 检查pip3
    if ! command -v pip3 &> /dev/null; then
        print_error "pip3 未安装，正在安装..."
        apt install -y python3-pip
    fi
    print_success "pip3 已安装"
    
    # 检查MySQL客户端
    if ! command -v mysql &> /dev/null; then
        print_info "安装MySQL客户端..."
        apt install -y mysql-client
    fi
    print_success "MySQL客户端已安装"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        print_info "安装Docker..."
        curl -fsSL https://get.docker.com -o get-docker.sh
        sh get-docker.sh
        systemctl enable docker
        systemctl start docker
        rm get-docker.sh
    fi
    print_success "Docker已安装"
}

# 安装Python依赖
install_python_dependencies() {
    print_header "安装Python依赖包"
    
    pip3 install --upgrade pip
    pip3 install flask mysql-connector-python pymysql requests
    
    print_success "Python依赖包安装完成"
}

# 备份现有部署
backup_existing() {
    if [ -d "$DEPLOY_DIR" ]; then
        print_header "备份现有部署"
        print_info "备份目录: $BACKUP_DIR"
        
        # 停止现有服务
        if [ -f "$DEPLOY_DIR/manage_services.sh" ]; then
            cd "$DEPLOY_DIR"
            ./manage_services.sh stop-all 2>/dev/null || true
        fi
        
        # 创建备份
        cp -r "$DEPLOY_DIR" "$BACKUP_DIR"
        print_success "现有部署已备份到 $BACKUP_DIR"
    fi
}

# 创建项目目录
create_project_directory() {
    print_header "创建项目目录"
    
    mkdir -p "$DEPLOY_DIR"
    cd "$DEPLOY_DIR"
    
    print_success "项目目录创建完成: $DEPLOY_DIR"
}

# 部署核心文件
deploy_core_files() {
    print_header "部署核心服务文件"
    
    # 检查当前目录是否有核心文件
    local current_dir=$(pwd)
    local files_found=0
    
    # 核心文件列表
    local core_files=(
        "gas_pipeline_inspection.py"
        "receiver_improved.py"
        "manage_services.sh"
        "clear_database.py"
        "README.md"
    )
    
    print_info "检查核心文件..."
    for file in "${core_files[@]}"; do
        if [ -f "$current_dir/$file" ]; then
            print_info "发现文件: $file"
            cp "$current_dir/$file" "$DEPLOY_DIR/"
            ((files_found++))
        else
            print_warning "文件不存在: $file"
        fi
    done
    
    if [ $files_found -eq 0 ]; then
        print_error "未找到任何核心文件，请确保在正确的目录运行脚本"
        exit 1
    fi
    
    # 设置执行权限
    chmod +x "$DEPLOY_DIR/manage_services.sh" 2>/dev/null || true
    chmod +x "$DEPLOY_DIR/clear_database.py" 2>/dev/null || true
    
    print_success "核心文件部署完成 ($files_found 个文件)"
}

# 配置MySQL数据库
setup_mysql() {
    print_header "配置MySQL数据库"
    
    print_info "请确保MySQL服务已启动并可访问"
    print_info "数据库配置信息:"
    echo "  - 用户: $MYSQL_USER"
    echo "  - 密码: $MYSQL_PASSWORD"
    echo "  - 数据库: $MYSQL_DATABASE"
    
    # 测试数据库连接
    print_info "测试数据库连接..."
    if mysql -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SELECT 1;" 2>/dev/null; then
        print_success "数据库连接正常"
    else
        print_warning "数据库连接失败，请手动配置数据库"
        print_info "请执行以下SQL命令:"
        echo "CREATE DATABASE IF NOT EXISTS $MYSQL_DATABASE CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        echo "CREATE USER IF NOT EXISTS '$MYSQL_USER'@'%' IDENTIFIED BY '$MYSQL_PASSWORD';"
        echo "GRANT ALL PRIVILEGES ON $MYSQL_DATABASE.* TO '$MYSQL_USER'@'%';"
        echo "FLUSH PRIVILEGES;"
        echo ""
        echo "CREATE TABLE IF NOT EXISTS $MYSQL_DATABASE.mqtt_messages ("
        echo "    id INT AUTO_INCREMENT PRIMARY KEY,"
        echo "    client_id VARCHAR(255),"
        echo "    topic VARCHAR(255),"
        echo "    payload TEXT,"
        echo "    arrived TIMESTAMP DEFAULT CURRENT_TIMESTAMP,"
        echo "    qos INT DEFAULT 0,"
        echo "    retain BOOLEAN DEFAULT FALSE,"
        echo "    INDEX idx_arrived (arrived),"
        echo "    INDEX idx_topic (topic),"
        echo "    INDEX idx_client_id (client_id)"
        echo ");"
        
        read -p "数据库配置完成后，按回车键继续..."
    fi
}

# 部署EMQX
deploy_emqx() {
    print_header "部署EMQX消息代理"
    
    # 检查EMQX是否已运行
    if docker ps | grep -q emqx; then
        print_info "EMQX容器已存在，正在重启..."
        docker restart emqx
    else
        print_info "启动EMQX容器..."
        docker run -d --name emqx \
            --restart=always \
            -p 1883:1883 \
            -p 8083:8083 \
            -p 8084:8084 \
            -p 8883:8883 \
            -p 18083:18083 \
            emqx/emqx:latest
    fi
    
    # 等待EMQX启动
    print_info "等待EMQX启动..."
    sleep 10
    
    # 检查EMQX状态
    if curl -s http://localhost:18083 > /dev/null; then
        print_success "EMQX启动成功"
        print_info "EMQX管理界面: http://$HOST_IP:18083"
        print_info "默认用户名: admin, 密码: public"
    else
        print_error "EMQX启动失败，请检查Docker状态"
    fi
}

# 配置系统服务
configure_system_service() {
    print_header "配置系统服务"
    
    # 创建systemd服务文件
    cat > /etc/systemd/system/gas-pipeline-monitor.service << EOF
[Unit]
Description=Gas Pipeline Monitoring System
After=network.target mysql.service docker.service
Requires=mysql.service docker.service

[Service]
Type=forking
User=root
WorkingDirectory=$DEPLOY_DIR
ExecStart=$DEPLOY_DIR/manage_services.sh start-all
ExecStop=$DEPLOY_DIR/manage_services.sh stop-all
ExecReload=$DEPLOY_DIR/manage_services.sh restart-all
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # 重载systemd配置
    systemctl daemon-reload
    systemctl enable gas-pipeline-monitor.service
    
    print_success "系统服务配置完成"
    print_info "服务管理命令:"
    echo "  启动: systemctl start gas-pipeline-monitor"
    echo "  停止: systemctl stop gas-pipeline-monitor"
    echo "  重启: systemctl restart gas-pipeline-monitor"
    echo "  状态: systemctl status gas-pipeline-monitor"
}

# 启动服务
start_services() {
    print_header "启动监控服务"
    
    cd "$DEPLOY_DIR"
    
    # 使用管理脚本启动服务
    if [ -f "manage_services.sh" ]; then
        ./manage_services.sh start-all
        sleep 5
        ./manage_services.sh status
    else
        print_error "管理脚本不存在，手动启动服务"
        nohup python3 receiver_improved.py > receiver.log 2>&1 &
        sleep 2
        nohup python3 gas_pipeline_inspection.py > gas_pipeline.log 2>&1 &
    fi
    
    print_success "服务启动完成"
}

# 验证部署
verify_deployment() {
    print_header "验证部署结果"
    
    # 检查服务端口
    local ports=("5000" "8082" "1883" "18083")
    for port in "${ports[@]}"; do
        if netstat -tlnp | grep -q ":$port "; then
            print_success "端口 $port 正在监听"
        else
            print_warning "端口 $port 未监听"
        fi
    done
    
    # 检查HTTP服务
    if curl -s http://localhost:5000/health > /dev/null; then
        print_success "MQTT接收服务正常"
    else
        print_warning "MQTT接收服务异常"
    fi
    
    if curl -s http://localhost:8082 > /dev/null; then
        print_success "Web监控界面正常"
    else
        print_warning "Web监控界面异常"
    fi
}

# 显示部署信息
show_deployment_info() {
    print_header "部署完成信息"
    
    echo -e "${GREEN}🎉 $PROJECT_NAME 部署完成！${NC}"
    echo ""
    echo -e "${BLUE}📍 部署信息:${NC}"
    echo "  项目目录: $DEPLOY_DIR"
    echo "  备份目录: $BACKUP_DIR (如果存在)"
    echo ""
    echo -e "${BLUE}🌐 访问地址:${NC}"
    echo "  监控系统: http://$HOST_IP:8082"
    echo "  EMQX管理: http://$HOST_IP:18083 (admin/public)"
    echo "  健康检查: http://$HOST_IP:5000/health"
    echo ""
    echo -e "${BLUE}🔧 服务管理:${NC}"
    echo "  查看状态: cd $DEPLOY_DIR && ./manage_services.sh status"
    echo "  重启服务: cd $DEPLOY_DIR && ./manage_services.sh restart-all"
    echo "  查看日志: cd $DEPLOY_DIR && ./manage_services.sh logs-web"
    echo ""
    echo -e "${BLUE}📊 EMQX配置:${NC}"
    echo "  1. 访问 http://$HOST_IP:18083"
    echo "  2. 登录 (admin/public)"
    echo "  3. 创建HTTP连接器: mysql-http-bridge"
    echo "  4. URL: http://$HOST_IP:5000/mqtt/data"
    echo "  5. 创建规则: SELECT * FROM \"location/+\""
    echo ""
    echo -e "${YELLOW}⚠️  重要提醒:${NC}"
    echo "  - 请确保防火墙开放端口: 5000, 8082, 1883, 18083"
    echo "  - 请配置EMQX连接器使用宿主机IP: $HOST_IP"
    echo "  - 系统已配置为开机自启动"
}

# 主函数
main() {
    print_header "$PROJECT_NAME - 一键部署脚本"
    
    echo -e "${BLUE}本脚本将部署以下组件:${NC}"
    echo "  ✅ MQTT接收服务 (receiver_improved.py)"
    echo "  ✅ Web监控界面 (gas_pipeline_inspection.py)"
    echo "  ✅ 服务管理脚本 (manage_services.sh)"
    echo "  ✅ EMQX消息代理 (Docker容器)"
    echo "  ✅ 系统服务配置"
    echo ""
    
    read -p "确认开始部署? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "部署已取消"
        exit 0
    fi
    
    # 执行部署步骤
    check_requirements
    install_python_dependencies
    backup_existing
    create_project_directory
    deploy_core_files
    setup_mysql
    deploy_emqx
    configure_system_service
    start_services
    verify_deployment
    show_deployment_info
    
    print_success "部署完成！"
}

# 错误处理
trap 'print_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 运行主函数
main "$@"
