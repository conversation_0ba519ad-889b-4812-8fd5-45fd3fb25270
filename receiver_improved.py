#!/usr/bin/env python3
# Version: 1.0 | Status: Active | Description: MQTT数据接收服务 (端口: 5000)
"""
改进的MQTT数据接收服务
修复了数据库字段和连接问题
"""

import json
import mysql.connector
from flask import Flask, request, jsonify
from datetime import datetime
import logging
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

app = Flask(__name__)
logger = logging.getLogger(__name__)

mysql_config = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'emqx',
    'password': 'EmqxPass!123',
    'database': 'emqx_data'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = mysql.connector.connect(**mysql_config)
        return conn
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def validate_mqtt_data(client_id, topic, payload, payload_str):
    """
    验证MQTT数据的完整性和有效性
    🔥 关键修复：确保只有有效数据才会被存储，避免ID递增但数据无效的问题
    """
    try:
        # 1. 基本字段验证
        if not client_id or client_id == 'unknown':
            return {"valid": False, "error": "client_id缺失或无效"}

        if not topic or topic == 'unknown':
            return {"valid": False, "error": "topic缺失或无效"}

        if not payload_str:
            return {"valid": False, "error": "payload为空"}

        # 2. Payload结构验证
        if isinstance(payload, dict):
            # 检查经纬度字段
            lat_fields = ['lat', 'latitude']
            lng_fields = ['lng', 'lon', 'longitude']

            lat_value = None
            lng_value = None

            # 查找纬度
            for field in lat_fields:
                if field in payload:
                    try:
                        lat_value = float(payload[field])
                        break
                    except (ValueError, TypeError):
                        continue

            # 查找经度
            for field in lng_fields:
                if field in payload:
                    try:
                        lng_value = float(payload[field])
                        break
                    except (ValueError, TypeError):
                        continue

            # 验证经纬度范围
            if lat_value is not None:
                if not (-90 <= lat_value <= 90):
                    return {"valid": False, "error": f"纬度超出范围: {lat_value}"}

            if lng_value is not None:
                if not (-180 <= lng_value <= 180):
                    return {"valid": False, "error": f"经度超出范围: {lng_value}"}

            # 如果有经纬度，检查是否在合理范围内（中国境内）
            if lat_value is not None and lng_value is not None:
                if not (3.86 <= lat_value <= 53.55 and 73.66 <= lng_value <= 135.05):
                    logger.warning(f"⚠️ 坐标不在中国境内: lat={lat_value}, lng={lng_value}")
                    # 不阻止存储，只是警告

        # 3. JSON格式最终验证
        try:
            json.loads(payload_str)
        except json.JSONDecodeError as e:
            return {"valid": False, "error": f"最终JSON格式验证失败: {str(e)}"}

        # 4. 数据长度验证
        if len(payload_str) > 65535:  # TEXT字段最大长度
            return {"valid": False, "error": "payload数据过长"}

        if len(client_id) > 255:
            return {"valid": False, "error": "client_id过长"}

        if len(topic) > 255:
            return {"valid": False, "error": "topic过长"}

        return {"valid": True, "error": None}

    except Exception as e:
        logger.error(f"数据验证过程中发生错误: {e}")
        return {"valid": False, "error": f"验证过程异常: {str(e)}"}

def ensure_table_exists():
    """确保数据表存在"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'mqtt_messages'")
        table_exists = cursor.fetchone() is not None
        
        if not table_exists:
            # 创建表
            create_table_sql = """
            CREATE TABLE mqtt_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                client_id VARCHAR(255),
                topic VARCHAR(255),
                payload TEXT,
                qos INT DEFAULT 0,
                arrived TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_topic (topic),
                INDEX idx_arrived (arrived)
            )
            """
            cursor.execute(create_table_sql)
            logger.info("✅ 创建 mqtt_messages 表")
        else:
            # 检查表结构
            cursor.execute("DESCRIBE mqtt_messages")
            columns = cursor.fetchall()
            column_names = [col[0] for col in columns]
            logger.info(f"📋 表字段: {column_names}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"检查/创建表失败: {e}")
        return False

@app.route('/mqtt/data', methods=['POST'])
def receive_mqtt_data():
    """接收MQTT数据"""
    try:
        # 获取原始数据
        raw_data = request.get_data(as_text=True)
        logger.info(f"收到原始数据: {raw_data}")
        
        # 尝试解析JSON
        try:
            data = json.loads(raw_data)
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            logger.error(f"原始数据: {repr(raw_data)}")

            # 尝试多种修复方法
            fixed_data = raw_data

            # 修复1: 处理常见的引号问题
            fixed_data = fixed_data.replace('\\"', '"').replace('"{', '{').replace('}"', '}')

            # 修复2: 移除控制字符
            import re
            fixed_data = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', fixed_data)

            # 修复3: 尝试修复缺少逗号的问题
            # 查找 "数字 "字段名" 的模式，在中间添加逗号
            fixed_data = re.sub(r'(\d+)\s*"([^"]+)":', r'\1, "\2":', fixed_data)
            # 查找 "} "字段名" 的模式，在中间添加逗号
            fixed_data = re.sub(r'}\s*"([^"]+)":', r'}, "\1":', fixed_data)
            # 查找 "字符串值" "字段名" 的模式
            fixed_data = re.sub(r'"\s*"([^"]+)":', r'", "\1":', fixed_data)

            logger.info(f"修复后数据: {repr(fixed_data)}")

            try:
                data = json.loads(fixed_data)
                logger.info("✅ JSON修复成功")
            except json.JSONDecodeError as e2:
                logger.error(f"修复后仍然失败: {e2}")

                # 最后尝试：手动解析关键字段
                try:
                    # 使用正则表达式提取关键信息
                    client_id_match = re.search(r'"client_id"\s*:\s*"([^"]*)"', raw_data)
                    topic_match = re.search(r'"topic"\s*:\s*"([^"]*)"', raw_data)
                    payload_match = re.search(r'"payload"\s*:\s*"([^"]*)"', raw_data)

                    if client_id_match and topic_match and payload_match:
                        # 手动构建数据对象
                        data = {
                            "client_id": client_id_match.group(1),
                            "topic": topic_match.group(1),
                            "payload": payload_match.group(1),
                            "qos": 1
                        }
                        logger.info("✅ 手动解析成功")
                    else:
                        return jsonify({
                            "status": "error",
                            "message": "JSON格式错误且无法修复",
                            "original_error": str(e),
                            "fixed_error": str(e2),
                            "raw_data": raw_data[:200]
                        }), 400
                except Exception as e3:
                    logger.error(f"手动解析也失败: {e3}")
                    return jsonify({
                        "status": "error",
                        "message": "所有解析方法都失败",
                        "errors": [str(e), str(e2), str(e3)],
                        "raw_data": raw_data[:200]
                    }), 400
        
        # 提取关键字段
        client_id = data.get('client_id', data.get('clientid', 'unknown'))
        topic = data.get('topic', 'unknown')
        payload = data.get('payload', {})
        qos = data.get('qos', 0)
        
        # 如果payload是字符串，尝试解析为JSON
        if isinstance(payload, str):
            try:
                # 清理payload中的控制字符和多余的空白字符
                cleaned_payload = payload.strip()
                # 移除换行符、制表符等控制字符
                cleaned_payload = cleaned_payload.replace('\n', '').replace('\r', '').replace('\t', '')
                # 移除多余的空格
                import re
                cleaned_payload = re.sub(r'\s+', ' ', cleaned_payload)

                logger.info(f"清理前payload: {repr(payload)}")
                logger.info(f"清理后payload: {repr(cleaned_payload)}")

                payload = json.loads(cleaned_payload)
                logger.info("✅ Payload JSON解析成功")
            except json.JSONDecodeError as e:
                logger.error(f"❌ Payload JSON格式错误: {e}")
                logger.error(f"❌ 原始payload: {repr(payload)}")

                # 尝试更激进的修复
                try:
                    # 移除所有控制字符，只保留可打印字符
                    import string
                    printable_payload = ''.join(char for char in payload if char in string.printable)
                    # 再次清理空白字符
                    printable_payload = re.sub(r'\s+', ' ', printable_payload.strip())
                    logger.info(f"激进清理后payload: {repr(printable_payload)}")

                    payload = json.loads(printable_payload)
                    logger.info("✅ 激进修复后JSON解析成功")
                except:
                    logger.error(f"❌ 所有修复尝试都失败")
                    return jsonify({
                        "status": "error",
                        "message": f"Payload JSON格式错误: {str(e)}",
                        "original_payload": repr(payload),
                        "cleaned_payload": repr(cleaned_payload) if 'cleaned_payload' in locals() else "N/A"
                    }), 400

        # 验证payload是否包含必要的地理位置信息
        if isinstance(payload, dict):
            # 检查是否有经纬度信息
            has_lat = any(key in payload for key in ['lat', 'latitude'])
            has_lng = any(key in payload for key in ['lng', 'lon', 'longitude'])

            if not (has_lat and has_lng):
                logger.warning(f"⚠️ Payload缺少经纬度信息: {payload}")
                # 不阻止存储，但记录警告

        # 将payload转换为JSON字符串存储
        try:
            payload_str = json.dumps(payload, ensure_ascii=False) if isinstance(payload, dict) else str(payload)
        except (TypeError, ValueError) as e:
            logger.error(f"❌ Payload序列化失败: {e}")
            return jsonify({
                "status": "error",
                "message": f"Payload序列化失败: {str(e)}"
            }), 400
        
        logger.info(f"解析后数据: client_id={client_id}, topic={topic}, payload={payload_str}")

        # 🔥 关键修复：在存储前进行完整的数据验证
        validation_result = validate_mqtt_data(client_id, topic, payload, payload_str)
        if not validation_result["valid"]:
            logger.error(f"❌ 数据验证失败: {validation_result['error']}")
            return jsonify({
                "status": "error",
                "message": f"数据验证失败: {validation_result['error']}",
                "validation_details": validation_result
            }), 400

        # 存储到数据库
        conn = get_db_connection()
        if not conn:
            return jsonify({"status": "error", "message": "Database connection failed"}), 500
        
        try:
            cursor = conn.cursor()

            # 🔥 关键修复：使用事务确保数据完整性
            conn.start_transaction()

            insert_sql = """
            INSERT INTO mqtt_messages (client_id, topic, payload, qos, arrived)
            VALUES (%s, %s, %s, %s, %s)
            """

            values = (client_id, topic, payload_str, qos, datetime.now())

            # 执行插入操作
            cursor.execute(insert_sql, values)
            record_id = cursor.lastrowid

            # 验证插入是否成功
            if cursor.rowcount != 1:
                raise Exception(f"插入操作异常，影响行数: {cursor.rowcount}")

            # 提交事务
            conn.commit()

            logger.info(f"✅ 数据已成功存储，记录ID: {record_id}")
            logger.info(f"✅ 存储内容: client_id={client_id}, topic={topic}")

            cursor.close()
            conn.close()

            return jsonify({
                "status": "success",
                "message": "Data received and stored successfully",
                "record_id": record_id,
                "client_id": client_id,
                "topic": topic
            }), 200

        except Exception as e:
            logger.error(f"❌ 数据库操作失败: {e}")
            logger.error(f"❌ 失败的数据: client_id={client_id}, topic={topic}, payload_length={len(payload_str)}")

            # 回滚事务
            if conn:
                try:
                    conn.rollback()
                    logger.info("事务已回滚")
                except:
                    pass
                finally:
                    conn.close()

            return jsonify({
                "status": "error",
                "message": f"Database operation failed: {str(e)}",
                "client_id": client_id,
                "topic": topic
            }), 500
        
    except Exception as e:
        logger.error(f"处理请求失败: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        conn = get_db_connection()
        if conn:
            conn.close()
            return jsonify({
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "database": "connected"
            }), 200
        else:
            return jsonify({
                "status": "unhealthy", 
                "timestamp": datetime.now().isoformat(),
                "database": "disconnected"
            }), 503
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """获取统计信息"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({"status": "error", "message": "Database connection failed"}), 500
        
        cursor = conn.cursor(dictionary=True)
        
        # 总消息数
        cursor.execute("SELECT COUNT(*) as total FROM mqtt_messages")
        total_count = cursor.fetchone()['total']
        
        # 位置消息数
        cursor.execute("SELECT COUNT(*) as location_count FROM mqtt_messages WHERE topic LIKE 'location/%'")
        location_count = cursor.fetchone()['location_count']
        
        # 最新消息时间
        cursor.execute("SELECT MAX(arrived) as latest FROM mqtt_messages")
        latest = cursor.fetchone()['latest']
        
        cursor.close()
        conn.close()
        
        return jsonify({
            "status": "success",
            "data": {
                "total_messages": total_count,
                "location_messages": location_count,
                "latest_message": latest.isoformat() if latest else None
            }
        }), 200
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

if __name__ == '__main__':
    logger.info("🚀 启动MQTT数据接收服务...")
    logger.info("📡 监听端口: 5000")
    logger.info("🔗 健康检查: http://127.0.0.1:5000/health")
    logger.info("📊 统计信息: http://127.0.0.1:5000/api/stats")
    
    # 确保表存在
    if ensure_table_exists():
        logger.info("✅ 数据库表检查完成")
    else:
        logger.error("❌ 数据库表检查失败")
    
    app.run(host='0.0.0.0', port=5000, debug=False)
