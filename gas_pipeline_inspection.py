#!/usr/bin/env python3
"""
天然气管道巡检系统
专为手持巡检设备设计的数据监控和地图显示系统
"""

import json
import mysql.connector
from flask import Flask, render_template_string, jsonify, request
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# MySQL配置
mysql_config = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'emqx',
    'password': 'EmqxPass!123',
    'database': 'emqx_data'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        return mysql.connector.connect(**mysql_config)
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def parse_sensor_data(payload_str):
    """解析传感器数据 - 支持多种精简格式"""
    try:
        result = {
            'latitude': None,
            'longitude': None,
            'timestamp': None,
            'sensors': {}
        }

        # 方案3：字符串格式 "lat,lng,temp,pressure,gas,humidity,timestamp"
        if isinstance(payload_str, str) and ',' in payload_str and not payload_str.strip().startswith('{'):
            try:
                parts = payload_str.strip().split(',')
                if len(parts) >= 2:
                    result['latitude'] = float(parts[0])
                    result['longitude'] = float(parts[1])

                    # 解析传感器数据（按顺序：温度、压力、气体浓度、湿度、时间戳）
                    sensor_names = ['temperature', 'pressure', 'gas_concentration', 'humidity']
                    for i, name in enumerate(sensor_names):
                        if i + 2 < len(parts):
                            try:
                                result['sensors'][name] = float(parts[i + 2])
                            except:
                                pass

                    # 时间戳
                    if len(parts) > 6:
                        try:
                            result['timestamp'] = int(parts[-1])
                        except:
                            pass

                return result
            except:
                pass

        # JSON格式解析
        if isinstance(payload_str, str):
            try:
                payload = json.loads(payload_str)
            except:
                return None
        else:
            payload = payload_str

        # 方案2：数组格式 [lat, lng, temp, pressure, gas, humidity, timestamp]
        if isinstance(payload, list) and len(payload) >= 2:
            result['latitude'] = float(payload[0])
            result['longitude'] = float(payload[1])

            # 解析传感器数据
            sensor_names = ['temperature', 'pressure', 'gas_concentration', 'humidity']
            for i, name in enumerate(sensor_names):
                if i + 2 < len(payload):
                    try:
                        result['sensors'][name] = float(payload[i + 2])
                    except:
                        pass

            # 时间戳
            if len(payload) > 6:
                try:
                    result['timestamp'] = int(payload[-1])
                except:
                    pass

            return result

        # 方案1：精简字段名格式
        if isinstance(payload, dict):
            # 提取经纬度（支持精简字段名）
            lat_keys = ['lat', 'latitude', 'y']
            lng_keys = ['lng', 'lon', 'longitude', 'x']

            for key in lat_keys:
                if key in payload:
                    result['latitude'] = float(payload[key])
                    break

            for key in lng_keys:
                if key in payload:
                    result['longitude'] = float(payload[key])
                    break

            # 提取时间戳
            time_keys = ['ts', 'timestamp', 'time', 't']
            for key in time_keys:
                if key in payload:
                    result['timestamp'] = payload[key]
                    break

            # 提取传感器数据（支持精简字段名和多种气体）
            sensor_mapping = {
                # 基础传感器
                't': 'temperature', 'temp': 'temperature', 'temperature': 'temperature',
                'h': 'humidity', 'hum': 'humidity', 'humidity': 'humidity',
                'p': 'pressure', 'press': 'pressure', 'pressure': 'pressure',

                # 通用气体浓度
                'g': 'gas_concentration', 'gas': 'gas_concentration', 'gas_concentration': 'gas_concentration',

                # 具体气体类型
                'co': 'co',           # 一氧化碳
                'co2': 'co2',         # 二氧化碳
                'ch4': 'ch4',         # 甲烷
                'h2': 'h2',           # 氢气
                'h2s': 'h2s',         # 硫化氢
                'sf6': 'sf6',         # 六氟化硫
                'nh3': 'nh3',         # 氨气
                'so2': 'so2',         # 二氧化硫
                'no2': 'no2',         # 二氧化氮
                'o2': 'o2',           # 氧气
                'o3': 'o3',           # 臭氧
                'c2h6': 'c2h6',       # 乙烷
                'c3h8': 'c3h8',       # 丙烷
                'c4h10': 'c4h10',     # 丁烷

                # 其他传感器
                'voc': 'voc',         # 挥发性有机化合物
                'pm25': 'pm25',       # PM2.5
                'pm10': 'pm10',       # PM10
                'noise': 'noise',     # 噪声
                'light': 'light',     # 光照
                'uv': 'uv'            # 紫外线
            }

            for key, value in payload.items():
                if key.lower() in sensor_mapping:
                    result['sensors'][sensor_mapping[key.lower()]] = value
                elif 'sensor' in key.lower():
                    result['sensors'][key] = value

        return result

    except Exception as e:
        logger.error(f"解析传感器数据失败: {e}")
        return None

@app.route('/')
def index():
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <title>天然气管道巡检监控系统</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: #f5f7fa;
            height: 100vh;
            overflow: hidden;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 12px 20px;
            border-bottom: 2px solid #34495e;
        }

        .header h1 {
            font-size: 18px;
            margin: 0;
            font-weight: normal;
            letter-spacing: 0.5px;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            opacity: 0.9;
            margin-top: 5px;
            flex-wrap: wrap;
        }

        .status-bar span {
            margin-right: 15px;
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 80px);
        }
        
        .data-panel {
            width: 35%;
            background: white;
            border-right: 1px solid #e1e8ed;
            display: flex;
            flex-direction: column;
        }
        
        .panel-header {
            background: #f8f9fa;
            padding: 8px 12px;
            border-bottom: 1px solid #e1e8ed;
            font-weight: bold;
            color: #2c3e50;
            font-size: 14px;
        }

        .controls {
            padding: 8px 12px;
            border-bottom: 1px solid #e1e8ed;
            background: #f8f9fa;
        }
        
        .control-group {
            margin-bottom: 10px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        /* 统一下拉框样式 */
        .control-group select {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            background: white;
            margin-bottom: 5px;
        }

        .calendar-controls {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #e1e8ed;
            margin-top: 10px;
        }

        .calendar-controls input[type="date"] {
            width: 100%;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 8px;
        }

        .date-range-info {
            background: #e3f2fd;
            padding: 8px;
            border-radius: 4px;
            border-left: 4px solid #2196f3;
            margin-top: 5px;
        }

        .date-range-info small {
            color: #1976d2;
            font-weight: bold;
        }
        
        .btn {
            background: #4a5568;
            color: white;
            border: 1px solid #2d3748;
            padding: 6px 12px;
            border-radius: 2px;
            cursor: pointer;
            margin-right: 8px;
            font-size: 12px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        .btn:hover {
            background: #2d3748;
        }

        /* 实时刷新按钮样式 */
        #realtime-btn {
            background: #2ecc71;
            transition: all 0.3s ease;
        }

        #realtime-btn:hover {
            background: #27ae60;
        }

        /* 调试按钮 - 生产环境可隐藏 */
        .debug-btn {
            display: inline-block; /* 临时显示用于调试 */
        }

        /* 通知动画 */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .data-list {
            flex: 1;
            overflow-y: auto;
            padding: 4px 6px;
            -webkit-overflow-scrolling: touch;
        }
        
        .data-item {
            border: 1px solid #e1e8ed;
            border-radius: 3px;
            margin-bottom: 1px;
            padding: 4px 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            line-height: 1.2;
            display: flex;
            align-items: center;
        }

        .data-item:hover {
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
            border-color: #3498db;
        }

        .data-item.selected {
            border-color: #3498db;
            background: #f0f8ff;
        }

        .data-item.highlighted {
            background: #fff3cd;
            border-color: #ffc107;
        }

        /* 消息状态颜色 */
        .data-item.status-new {
            background: #d4edda;
            border-color: #28a745;
        }

        .data-item.status-critical {
            background: #f8d7da;
            border-color: #dc3545;
        }

        .data-item.status-read {
            background: white;
            border-color: #e1e8ed;
        }
        
        .item-content {
            flex: 1;
            min-width: 0;
        }

        .item-header {
            display: flex;
            align-items: center;
            margin-bottom: 2px;
        }

        .item-checkbox {
            margin-right: 6px;
            transform: scale(1.0);
        }

        .item-title {
            font-weight: bold;
            color: #2c3e50;
            flex: 1;
            font-size: 14px;
        }

        .item-time {
            font-size: 11px;
            color: #7f8c8d;
        }

        .item-location {
            font-size: 12px;
            color: #34495e;
            margin-bottom: 3px;
        }
        
        .item-sensors {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 2px;
        }

        .sensor-tag {
            background: #ecf0f1;
            padding: 2px 6px;
            border-radius: 6px;
            font-size: 10px;
            color: #2c3e50;
        }

        /* 气体浓度特殊显示 */
        .sensor-tag.gas-concentration {
            background: #e8f5e8;
            color: #2d5a2d;
            font-weight: bold;
            border: 1px solid #4caf50;
            font-size: 11px;
            padding: 3px 8px;
        }

        .sensor-tag.gas-concentration.warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffc107;
            animation: pulse 2s infinite;
        }

        .sensor-tag.gas-concentration.danger {
            background: #f8d7da;
            color: #721c24;
            border-color: #dc3545;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .sensor-tag.warning {
            background: #fff3cd;
            color: #856404;
        }

        .sensor-tag.danger {
            background: #f8d7da;
            color: #721c24;
        }

        /* 状态操作按钮 */
        .status-buttons {
            display: flex;
            flex-direction: column;
            gap: 1px;
            margin-left: 6px;
            flex-shrink: 0;
        }

        .status-btn {
            padding: 2px 6px;
            border: none;
            border-radius: 2px;
            font-size: 9px;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 35px;
            text-align: center;
        }

        .status-btn.critical {
            background: #dc3545;
            color: white;
        }

        .status-btn.read {
            background: #6c757d;
            color: white;
        }

        .status-btn.delete {
            background: #dc3545;
            color: white;
        }

        .status-btn:hover {
            opacity: 0.8;
        }

        /* 自定义地图标记样式 */
        .custom-data-marker {
            background: transparent !important;
            border: none !important;
        }

        .custom-data-marker .data-box,
        .custom-data-marker .multi-gas-box {
            transition: all 0.2s ease;
            transform-origin: center bottom;
        }

        .custom-data-marker:hover .data-box,
        .custom-data-marker:hover .multi-gas-box,
        .custom-data-marker:active .data-box,
        .custom-data-marker:active .multi-gas-box {
            transform: scale(1.1);
            z-index: 1000;
        }

        .custom-data-marker .pointer {
            transition: all 0.2s ease;
        }

        /* 多气体标记特殊样式 */
        .multi-gas-box {
            position: relative;
            word-wrap: break-word;
            overflow: visible;
        }

        .marker-container {
            position: relative;
        }

        .multi-gas-box .gas-item {
            margin: 2px 0;
            line-height: 1.2;
            white-space: nowrap;
        }

        .multi-gas-box .gas-name {
            font-size: 9px;
            opacity: 0.95;
            font-weight: bold;
        }

        .multi-gas-box .gas-value {
            font-size: 11px;
            font-weight: bold;
        }

        .multi-gas-box .more-indicator {
            font-size: 8px;
            opacity: 0.9;
            margin-top: 3px;
            font-style: italic;
        }

        /* 确保标记内容不被截断 */
        .custom-data-marker .multi-gas-box {
            overflow: visible !important;
            white-space: normal !important;
        }

        /* 移动端触摸优化 */
        @media (max-width: 768px) {
            .btn, .status-btn {
                min-height: 32px;
                touch-action: manipulation;
            }

            .item-checkbox {
                min-width: 20px;
                min-height: 20px;
            }

            .data-item {
                touch-action: manipulation;
                -webkit-tap-highlight-color: rgba(0,0,0,0.1);
            }

            .custom-data-marker .data-box {
                min-width: 45px;
                min-height: 30px;
            }
        }
        
        .map-container {
            width: 65%;
            position: relative;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        .map-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 1000;
        }
        
        .loading {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stats {
            display: flex;
            gap: 20px;
            font-size: 14px;
        }
        
        .stat-item {
            color: rgba(255,255,255,0.9);
        }
        
        .stat-value {
            font-weight: bold;
            font-size: 16px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .data-panel {
                width: 38%;
            }
            .map-container {
                width: 62%;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                height: calc(100vh - 50px);
            }

            .data-panel {
                width: 100%;
                height: 60vh;
                border-right: none;
                border-bottom: 2px solid #e1e8ed;
                order: 1;
            }

            .map-container {
                width: 100%;
                height: 40vh;
                order: 2;
            }

            .header {
                padding: 6px 12px;
            }

            .header h1 {
                font-size: 15px;
                margin-bottom: 4px;
            }

            .status-bar {
                font-size: 10px;
                flex-wrap: wrap;
                gap: 6px;
            }

            .status-bar span {
                margin-right: 8px;
            }

            .controls {
                padding: 2px 4px;
            }

            .control-group {
                margin-bottom: 2px;
                flex-wrap: wrap;
                align-items: center;
                width: 100%;
            }

            .control-group label {
                font-size: 10px;
                margin-right: 2px;
                white-space: nowrap;
                flex-shrink: 0;
            }

            .control-group select {
                font-size: 10px;
                padding: 4px 6px;
                margin-right: 2px;
                width: 100%;
                min-width: 100px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background: white;
            }

            /* 按钮行布局优化 */
            .button-row {
                display: flex;
                justify-content: space-between;
                gap: 1px;
                margin-bottom: 2px;
            }

            .btn {
                padding: 2px 4px;
                font-size: 9px;
                margin: 0;
                white-space: nowrap;
                flex: 1;
                text-align: center;
                min-width: 0;
            }

            .data-item {
                padding: 3px 4px;
                font-size: 11px;
                margin-bottom: 0px;
                line-height: 1.0;
                border-bottom: 1px solid #f0f0f0;
            }

            .item-content {
                flex: 1;
                min-width: 0;
            }

            .item-header {
                margin-bottom: 1px;
            }

            .item-title {
                font-size: 12px;
                font-weight: bold;
            }

            .item-time {
                font-size: 9px;
            }

            .item-location {
                font-size: 10px;
                margin-bottom: 1px;
            }

            .sensor-tag {
                font-size: 9px;
                padding: 1px 3px;
                margin-right: 2px;
                margin-bottom: 1px;
            }

            .status-buttons {
                margin-left: 4px;
                gap: 1px;
                flex-shrink: 0;
            }

            .status-btn {
                padding: 2px 5px;
                font-size: 9px;
                min-width: 32px;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 480px) {
            .data-panel {
                height: 65vh;
            }

            .map-container {
                height: 35vh;
            }

            .header {
                padding: 4px 8px;
            }

            .header h1 {
                font-size: 13px;
                margin-bottom: 2px;
            }

            .status-bar {
                font-size: 8px;
            }

            .status-bar span {
                margin-right: 6px;
            }

            .controls {
                padding: 3px 4px;
            }

            .control-group {
                margin-bottom: 3px;
            }

            .control-group label {
                font-size: 9px;
                margin-right: 2px;
            }

            .control-group select {
                font-size: 9px;
                padding: 3px 4px;
                margin-right: 2px;
                width: 100%;
                min-width: 80px;
                border: 1px solid #ddd;
                border-radius: 2px;
                background: white;
            }

            .btn {
                padding: 2px 3px;
                font-size: 9px;
                margin: 0;
                flex: 1;
                text-align: center;
            }

            .data-item {
                padding: 1px 3px;
                margin-bottom: 0px;
                font-size: 9px;
                line-height: 1.0;
            }

            .item-title {
                font-size: 10px;
            }

            .item-time {
                font-size: 7px;
            }

            .item-location {
                font-size: 8px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .sensor-tag {
                font-size: 7px;
                padding: 1px 3px;
            }

            .status-btn {
                padding: 1px 3px;
                font-size: 7px;
                min-width: 30px;
            }

            .item-content {
                min-width: 0;
                overflow: hidden;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>天然气管道巡检监控系统</h1>
        <div class="status">
            <span id="connection-status">正在连接...</span>
            <div class="stats">
                <div class="stat-item">
                    总记录: <span class="stat-value" id="total-records">0</span>
                </div>
                <div class="stat-item">
                    已选择: <span class="stat-value" id="selected-count">0</span>
                </div>
                <div class="stat-item">
                    最后更新: <span class="stat-value" id="last-update">--</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="main-container">
        <div class="data-panel">
            <div class="panel-header">
                巡检数据列表
            </div>
            
            <div class="controls">
                <div class="button-row">
                    <button class="btn" onclick="selectAll()">全选</button>
                    <button class="btn" onclick="clearSelection()">清空</button>
                    <button class="btn" onclick="refreshData()">刷新</button>
                    <button class="btn" onclick="toggleCalendar()">日期选择</button>
                    <button class="btn" id="realtime-btn" onclick="toggleRealTime()">实时: 开</button>
                    <button class="btn debug-btn" onclick="debugMessageIds()" style="background: #f39c12;">调试</button>
                </div>
                <div class="control-group">
                    <label>排序方式:</label>
                    <select id="sort-select" onchange="applySorting()">
                        <option value="time_desc">时间 (降序)</option>
                        <option value="time_asc">时间 (升序)</option>
                        <option value="gas_desc">浓度 (降序)</option>
                        <option value="gas_asc">浓度 (升序)</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>浓度筛选:</label>
                    <select id="gas-filter" onchange="applyGasFilter()">
                        <option value="all">全部浓度</option>
                        <option value="0-500">0-500 ppm (正常)</option>
                        <option value="500-2000">500-2000 ppm (警告)</option>
                        <option value="2000-999999">2000+ ppm (危险)</option>
                        <option value="custom">自定义范围</option>
                    </select>
                    <input type="number" id="gas-min" placeholder="最小值" style="width: 60px; padding: 2px; margin-left: 4px; display: none;">
                    <input type="number" id="gas-max" placeholder="最大值" style="width: 60px; padding: 2px; margin-left: 2px; display: none;">
                </div>
                <div class="button-row">
                    <button class="btn" onclick="markAllAsRead()" style="background: #6c757d; color: white;">全部已读</button>
                    <button class="btn" onclick="markSelectedAsCritical()" style="background: #dc3545; color: white;">标记</button>
                    <button class="btn" onclick="deleteSelected()" style="background: #dc3545; color: white;">删除选中</button>
                    <button class="btn" onclick="fitMapToMarkers()" style="background: #17a2b8; color: white;">适应视野</button>
                </div>
                <div class="control-group">
                    <label>快速选择:</label>
                    <select id="time-filter" onchange="filterData()">
                        <option value="1h">最近1小时</option>
                        <option value="6h">最近6小时</option>
                        <option value="24h" selected>最近24小时</option>
                        <option value="7d">最近7天</option>
                        <option value="30d">最近30天</option>
                        <option value="all">全部数据</option>
                        <option value="custom">自定义日期</option>
                    </select>
                </div>
                <div class="control-group calendar-controls" id="calendar-controls" style="display: none;">
                    <label>开始日期:</label>
                    <input type="date" id="start-date" onchange="filterData()">
                    <label>结束日期:</label>
                    <input type="date" id="end-date" onchange="filterData()">
                </div>
                <div class="control-group" id="date-range-display" style="display: none;">
                    <div class="date-range-info">
                        <small id="date-range-text">选择日期范围</small>
                    </div>
                </div>
            </div>
            
            <div class="data-list" id="data-list">
                <div class="map-loading">
                    <div class="loading"></div>
                    <p>正在加载巡检数据...</p>
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div class="map-loading" id="map-loading">
                <div class="loading"></div>
                <p>正在初始化地图...</p>
            </div>
            <div id="map"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let map;
        let markers = [];
        let inspectionData = [];
        let selectedItems = new Set();
        let mapInitialized = false;

        // 气体类型中文名称映射
        const gasNameMapping = {
            // 通用气体
            'gas_concentration': '气体浓度',
            'gas': '气体浓度',
            'g': '气体浓度',

            // 具体气体类型
            'co': '一氧化碳',
            'co2': '二氧化碳',
            'ch4': '甲烷',
            'h2': '氢气',
            'h2s': '硫化氢',
            'sf6': '六氟化硫',
            'nh3': '氨气',
            'so2': '二氧化硫',
            'no2': '二氧化氮',
            'o2': '氧气',
            'o3': '臭氧',
            'c2h6': '乙烷',
            'c3h8': '丙烷',
            'c4h10': '丁烷',
            'voc': '挥发性有机物',

            // 其他传感器
            'temperature': '温度',
            'temp': '温度',
            't': '温度',
            'humidity': '湿度',
            'hum': '湿度',
            'h': '湿度',
            'pressure': '压力',
            'press': '压力',
            'p': '压力',
            'pm25': 'PM2.5',
            'pm10': 'PM10',
            'noise': '噪声',
            'light': '光照',
            'uv': '紫外线'
        };

        // 气体单位映射
        const gasUnitMapping = {
            'co': 'ppm',
            'co2': 'ppm',
            'ch4': 'ppm',
            'h2': 'ppm',
            'h2s': 'ppm',
            'sf6': 'ppm',
            'nh3': 'ppm',
            'so2': 'ppm',
            'no2': 'ppm',
            'o2': '%',
            'o3': 'ppm',
            'c2h6': 'ppm',
            'c3h8': 'ppm',
            'c4h10': 'ppm',
            'voc': 'ppm',
            'gas_concentration': 'ppm',
            'gas': 'ppm',
            'g': 'ppm',
            'temperature': '°C',
            'temp': '°C',
            't': '°C',
            'humidity': '%',
            'hum': '%',
            'h': '%',
            'pressure': 'MPa',
            'press': 'MPa',
            'p': 'MPa',
            'pm25': 'μg/m³',
            'pm10': 'μg/m³',
            'noise': 'dB',
            'light': 'lux',
            'uv': 'mW/cm²'
        };

        // 地图状态保存
        let mapState = {
            center: [39.9042, 116.4074], // 默认北京中心
            zoom: 10,
            lastSaved: null
        };

        // 判断是否为气体类型
        function isGasType(key) {
            const gasTypes = ['gas', 'g', 'concentration', 'co', 'co2', 'ch4', 'h2', 'h2s', 'sf6',
                             'nh3', 'so2', 'no2', 'o2', 'o3', 'c2h6', 'c3h8', 'c4h10', 'voc'];
            return gasTypes.some(type => key.toLowerCase().includes(type)) ||
                   key.toLowerCase().includes('gas') ||
                   key.toLowerCase().includes('浓度');
        }

        // 获取不同气体的危险阈值
        function getGasThresholds(key) {
            const thresholds = {
                // 通用气体
                'gas': { warning: 500, danger: 1000 },
                'g': { warning: 500, danger: 1000 },
                'gas_concentration': { warning: 500, danger: 1000 },

                // 具体气体类型 (ppm)
                'co': { warning: 50, danger: 100 },      // 一氧化碳
                'co2': { warning: 5000, danger: 10000 }, // 二氧化碳
                'ch4': { warning: 1000, danger: 5000 },  // 甲烷
                'h2': { warning: 1000, danger: 4000 },   // 氢气
                'h2s': { warning: 10, danger: 20 },      // 硫化氢
                'sf6': { warning: 1000, danger: 5000 },  // 六氟化硫
                'nh3': { warning: 25, danger: 50 },      // 氨气
                'so2': { warning: 5, danger: 10 },       // 二氧化硫
                'no2': { warning: 3, danger: 5 },        // 二氧化氮
                'o2': { warning: 16, danger: 12 },       // 氧气 (低氧危险)
                'o3': { warning: 0.1, danger: 0.2 },     // 臭氧
                'c2h6': { warning: 1000, danger: 3000 }, // 乙烷
                'c3h8': { warning: 1000, danger: 2000 }, // 丙烷
                'c4h10': { warning: 800, danger: 1600 }, // 丁烷
                'voc': { warning: 300, danger: 1000 }    // 挥发性有机物
            };

            const keyLower = key.toLowerCase();
            return thresholds[keyLower] || { warning: 500, danger: 1000 };
        }
        
        // 初始化地图
        function initMap() {
            try {
                console.log('初始化地图...');

                // 加载保存的地图状态
                const hasMapState = loadMapState();

                // 创建地图实例，使用保存的状态或默认状态
                const initialCenter = hasMapState ? mapState.center : [39.915, 116.404];
                const initialZoom = hasMapState ? mapState.zoom : 10;

                map = L.map('map', {
                    minZoom: 3,
                    maxZoom: 18
                }).setView(initialCenter, initialZoom);

                // 添加高德地图瓦片层
                L.tileLayer('https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', {
                    attribution: '© 高德地图',
                    subdomains: ['1', '2', '3', '4'],
                    maxZoom: 18
                }).addTo(map);

                // 添加地图移动和缩放事件监听器
                map.on('moveend zoomend', function() {
                    // 延迟保存，避免频繁保存
                    clearTimeout(window.mapSaveTimeout);
                    window.mapSaveTimeout = setTimeout(saveMapState, 1000);
                });

                // 隐藏加载提示
                document.getElementById('map-loading').style.display = 'none';
                mapInitialized = true;

                console.log('地图初始化完成');

                // 立即加载数据
                loadInspectionData();
                
            } catch (error) {
                console.error('地图初始化失败:', error);
                document.getElementById('map-loading').innerHTML = 
                    '<p style="color: #e74c3c;">地图加载失败<br><small>请检查网络连接</small></p>';
            }
        }
        
        // 检查新消息
        function checkNewMessages() {
            if (!isRealTimeEnabled) {
                console.log('实时刷新已关闭，跳过检查');
                return;
            }

            console.log(`检查新消息，当前lastMessageId: ${lastMessageId}`);

            fetch(`/api/inspection-data?since_id=${lastMessageId}&limit=10`)
                .then(response => response.json())
                .then(data => {
                    console.log('检查新消息响应:', data);

                    if (data.status === 'success') {
                        if (data.data.length > 0) {
                            console.log(`检测到 ${data.data.length} 条新消息`);

                            // 更新最后消息ID
                            const newMaxId = Math.max(...data.data.map(item => item.id));
                            console.log(`更新lastMessageId: ${lastMessageId} -> ${newMaxId}`);
                            lastMessageId = newMaxId;

                            // 合并新数据到现有数据
                            const newData = data.data.filter(newItem =>
                                !allData.some(existingItem => existingItem.id === newItem.id)
                            );

                            if (newData.length > 0) {
                                console.log(`合并 ${newData.length} 条新数据`);
                                allData = [...newData, ...allData];

                                // 应用排序
                                sortData(allData);

                                // 更新inspectionData供其他函数使用
                                inspectionData = allData;

                                // 更新数据列表显示
                                updateDataList(allData);

                                // 🔥 关键修复：重新从服务器获取准确的统计数据
                                // 而不是使用本地计算，避免统计错误
                                fetch('/api/stats')
                                    .then(response => response.json())
                                    .then(statsData => {
                                        if (statsData.status === 'success') {
                                            updateStats(statsData.stats);
                                            console.log('更新统计数据:', statsData.stats);
                                        }
                                    })
                                    .catch(error => {
                                        console.error('获取统计数据失败:', error);
                                        // 降级使用本地统计
                                        updateStats({total: allData.length});
                                    });

                                // 更新地图标记
                                updateMapMarkers();

                                // 显示新消息提示
                                showNewMessageNotification(newData.length);
                            } else {
                                console.log('没有真正的新数据需要合并');
                            }
                        } else {
                            console.log('没有检测到新消息');
                        }
                    } else {
                        console.error('API返回错误:', data.message);
                    }
                })
                .catch(error => {
                    console.error('检查新消息失败:', error);
                });
        }

        // 显示新消息通知
        function showNewMessageNotification(count) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #2ecc71;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 10000;
                font-size: 14px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                animation: slideIn 0.3s ease-out;
            `;
            notification.textContent = `🔔 收到 ${count} 条新消息`;
            document.body.appendChild(notification);

            // 3秒后自动消失
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }
            }, 3000);
        }

        // 切换实时刷新
        function toggleRealTime() {
            isRealTimeEnabled = !isRealTimeEnabled;
            const btn = document.getElementById('realtime-btn');

            if (isRealTimeEnabled) {
                btn.textContent = '实时: 开';
                btn.style.background = '#2ecc71';
                // 重新启动实时检查
                if (realTimeCheckInterval) {
                    clearInterval(realTimeCheckInterval);
                }
                realTimeCheckInterval = setInterval(checkNewMessages, 5000);
                console.log('实时刷新已开启，当前lastMessageId:', lastMessageId);

                // 立即检查一次
                setTimeout(checkNewMessages, 1000);
            } else {
                btn.textContent = '实时: 关';
                btn.style.background = '#95a5a6';
                // 停止实时检查
                if (realTimeCheckInterval) {
                    clearInterval(realTimeCheckInterval);
                    realTimeCheckInterval = null;
                }
                console.log('实时刷新已关闭');
            }
        }

        // 调试函数：检查消息ID范围
        function debugMessageIds() {
            console.log('=== 消息ID调试信息 ===');
            console.log('当前lastMessageId:', lastMessageId);
            console.log('当前allData长度:', allData.length);
            if (allData.length > 0) {
                const ids = allData.map(item => item.id).sort((a, b) => b - a);
                console.log('当前数据ID范围:', ids[0], '-', ids[ids.length - 1]);
                console.log('前5个ID:', ids.slice(0, 5));
            }

            // 测试API
            fetch('/api/inspection-data?since_id=' + lastMessageId + '&limit=5')
                .then(response => response.json())
                .then(data => {
                    console.log('测试API响应:', data);
                })
                .catch(error => {
                    console.error('测试API失败:', error);
                });
        }

        // 加载巡检数据（优化版 - 支持分页和排序）
        function loadInspectionData() {
            const timeFilter = document.getElementById('time-filter').value;
            let url = `/api/inspection-data?time_filter=${timeFilter}`;

            // 添加分页参数（除非显示全部）
            if (timeFilter === 'all' || isLoadingAll) {
                url += `&limit=all`;
            } else {
                url += `&page=${currentPage}&limit=${pageSize}`;
            }

            // 如果是自定义日期，添加日期参数
            if (timeFilter === 'custom') {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;

                if (startDate && endDate) {
                    url += `&start_date=${startDate}&end_date=${endDate}`;
                    updateDateRangeDisplay(startDate, endDate);
                } else {
                    document.getElementById('date-range-text').textContent = '请选择开始和结束日期';
                    return;
                }
            } else {
                document.getElementById('date-range-display').style.display = 'none';
            }

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // 调试：打印服务器返回的数据
                        console.log('服务器返回数据:', {
                            dataLength: data.data.length,
                            stats: data.stats,
                            currentAllDataLength: allData.length
                        });

                        if (currentPage === 1 || isLoadingAll) {
                            // 首次加载或显示全部时替换数据
                            allData = data.data;
                        } else {
                            // 分页加载时追加数据
                            allData = allData.concat(data.data);
                        }

                        inspectionData = allData;

                        // 更新最后消息ID
                        if (allData.length > 0) {
                            const newMaxId = Math.max(...allData.map(item => item.id));
                            lastMessageId = newMaxId;
                            console.log(`初始化lastMessageId: ${lastMessageId}`);
                        } else {
                            // 如果没有数据，从数据库获取最大ID
                            fetch('/api/max-message-id')
                                .then(response => response.json())
                                .then(result => {
                                    if (result.status === 'success' && result.max_id) {
                                        lastMessageId = result.max_id;
                                        console.log(`从数据库获取lastMessageId: ${lastMessageId}`);
                                    }
                                })
                                .catch(error => {
                                    console.error('获取最大消息ID失败:', error);
                                });
                        }

                        sortData(allData);
                        updateDataList(allData);

                        // 确保统计数字正确：如果本地数据为空，强制更新统计为0
                        if (allData.length === 0) {
                            updateStats({total: 0});
                            console.log('数据为空，强制更新统计为0');
                        } else {
                            updateStats(data.stats);
                        }

                        updateMapMarkers();
                        updateButtonStates();

                        document.getElementById('connection-status').textContent = '在线';
                        document.getElementById('last-update').textContent = new Date().toLocaleTimeString();

                        // 显示查询结果统计
                        if (data.stats.date_range) {
                            updateDateRangeDisplay(data.stats.date_range.start, data.stats.date_range.end, data.stats.total);
                        }
                    } else {
                        throw new Error(data.message || '数据加载失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('connection-status').textContent = '连接失败';
                });
        }

        // 更新数据列表
        function updateDataList(data) {
            const listContainer = document.getElementById('data-list');

            if (data.length === 0) {
                listContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #7f8c8d;">暂无巡检数据</div>';
                return;
            }

            let html = '';
            data.forEach((item, index) => {
                const isSelected = selectedItems.has(item.id);
                const sensorTags = generateSensorTags(item.sensors);

                // 获取消息状态（优先使用本地存储的状态）
                let messageStatus = messageStatuses[item.id];

                // 如果没有本地状态记录，使用默认状态
                if (!messageStatus) {
                    messageStatus = item.status || 'new';
                    // 初始化时不保存到messageStatuses，保持原始状态
                }

                const statusClass = `status-${messageStatus}`;

                // 确保每个消息都有原始状态记录（只记录一次）
                if (!originalStatuses[item.id]) {
                    // 如果消息状态不是critical，记录当前状态为原始状态
                    // 如果是critical，说明已经被标记过，不应该覆盖原始状态
                    if (messageStatus !== 'critical') {
                        originalStatuses[item.id] = messageStatus;
                    } else {
                        // 如果当前是critical但没有原始状态记录，默认为read
                        originalStatuses[item.id] = 'read';
                    }
                }

                // 确定标记按钮的文字
                const criticalBtnText = messageStatus === 'critical' ? '取消' : '标记';

                html += `
                    <div class="data-item ${isSelected ? 'selected' : ''} ${statusClass}" data-id="${item.id}" data-index="${index}" data-status="${messageStatus}">
                        <div class="item-content">
                            <div class="item-header">
                                <input type="checkbox" class="item-checkbox" ${isSelected ? 'checked' : ''}
                                       onchange="toggleSelection(${item.id})">
                                <div class="item-title">${item.topic || '巡检设备'}</div>
                                <div class="item-time">${item.time}</div>
                            </div>
                            <div class="item-location">
                                位置: ${item.latitude ? item.latitude.toFixed(6) : 'N/A'}, ${item.longitude ? item.longitude.toFixed(6) : 'N/A'}
                            </div>
                            <div class="item-sensors">
                                ${sensorTags}
                            </div>
                        </div>
                        <div class="status-buttons">
                            <button class="status-btn critical" onclick="setMessageStatus(${item.id}, 'critical', event)" title="标记为重要">${criticalBtnText}</button>
                            <button class="status-btn read" onclick="setMessageStatus(${item.id}, 'read', event)" title="标记为已读">已读</button>
                            <button class="status-btn delete" onclick="deleteMessage(${item.id}, event)" title="删除此条数据">删除</button>
                        </div>
                    </div>
                `;
            });

            listContainer.innerHTML = html;

            // 添加点击事件
            document.querySelectorAll('.data-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    if (e.target.type !== 'checkbox') {
                        const index = parseInt(this.dataset.index);
                        jumpToLocation(index);
                    }
                });
            });

            // 更新按钮状态
            updateButtonStates();
        }

        // 更新按钮状态
        function updateButtonStates() {
            // 更新全选按钮
            const selectAllBtn = document.querySelector('button[onclick="selectAll()"]');
            if (selectAllBtn) {
                if (inspectionData.length === 0) {
                    // 没有数据时，按钮显示"全选"但添加视觉提示
                    selectAllBtn.textContent = '全选';
                    selectAllBtn.style.opacity = '0.6';
                    selectAllBtn.title = '没有数据可选择';
                } else if (selectedItems.size === inspectionData.length && inspectionData.length > 0) {
                    selectAllBtn.textContent = '取消全选';
                    selectAllBtn.style.opacity = '1';
                    selectAllBtn.title = '';
                } else {
                    selectAllBtn.textContent = '全选';
                    selectAllBtn.style.opacity = '1';
                    selectAllBtn.title = '';
                }
            }

            // 更新标记按钮
            const criticalBtn = document.querySelector('button[onclick="markSelectedAsCritical()"]');
            if (criticalBtn && selectedItems.size > 0) {
                const selectedArray = Array.from(selectedItems);
                const allCritical = selectedArray.every(id => messageStatuses[id] === 'critical');
                criticalBtn.textContent = allCritical ? '取消' : '标记';
            } else if (criticalBtn) {
                criticalBtn.textContent = '标记';
            }
        }

        // 生成传感器标签
        function generateSensorTags(sensors) {
            if (!sensors || Object.keys(sensors).length === 0) {
                return '<span class="sensor-tag">无数据</span>';
            }

            let tags = '';
            for (const [key, value] of Object.entries(sensors)) {
                let tagClass = 'sensor-tag';

                // 获取中文名称和单位
                const chineseName = gasNameMapping[key.toLowerCase()] || key;
                const unit = gasUnitMapping[key.toLowerCase()] || '';

                // 气体类型特殊处理 - 更显眼
                if (isGasType(key)) {
                    tagClass = 'sensor-tag gas-concentration';

                    const gasValue = parseFloat(value);
                    // 根据不同气体类型设置不同的危险阈值
                    const thresholds = getGasThresholds(key);
                    if (gasValue > thresholds.danger) tagClass += ' danger';
                    else if (gasValue > thresholds.warning) tagClass += ' warning';

                    // 气体浓度使用特殊格式，显示中文名称
                    tags += `<span class="${tagClass}">${chineseName}: ${value} ${unit}</span>`;
                    continue;
                }

                // 其他传感器的警告级别
                const sensorValue = parseFloat(value);

                // 温度传感器
                if (key.toLowerCase().includes('temp') || key === 't') {
                    if (sensorValue > 60 || sensorValue < -10) tagClass += ' danger';
                    else if (sensorValue > 40 || sensorValue < 0) tagClass += ' warning';
                }
                // 湿度传感器
                else if (key.toLowerCase().includes('hum') || key.toLowerCase().includes('humidity') || key === 'h') {
                    if (sensorValue > 90 || sensorValue < 10) tagClass += ' warning';
                }
                // 压力传感器
                else if (key.toLowerCase().includes('press') || key.toLowerCase().includes('pressure') || key === 'p') {
                    if (sensorValue > 2.0 || sensorValue < 0.5) tagClass += ' danger';
                    else if (sensorValue > 1.5 || sensorValue < 0.8) tagClass += ' warning';
                }
                // PM2.5/PM10
                else if (key.toLowerCase().includes('pm')) {
                    if (sensorValue > 150) tagClass += ' danger';
                    else if (sensorValue > 75) tagClass += ' warning';
                }
                // 噪声
                else if (key.toLowerCase().includes('noise')) {
                    if (sensorValue > 85) tagClass += ' danger';
                    else if (sensorValue > 70) tagClass += ' warning';
                }

                // 使用中文名称和对应单位显示
                tags += `<span class="${tagClass}">${chineseName}: ${value} ${unit}</span>`;
            }

            return tags;
        }

        // 更新统计信息
        function updateStats(stats) {
            document.getElementById('total-records').textContent = stats.total || 0;
            document.getElementById('selected-count').textContent = selectedItems.size;
        }

        // 切换选择状态
        function toggleSelection(id) {
            const wasSelected = selectedItems.has(id);

            if (wasSelected) {
                selectedItems.delete(id);
            } else {
                selectedItems.add(id);

                // 如果是新选中的项目，自动跳转到对应位置
                const dataItem = inspectionData.find(item => item.id === id);
                if (dataItem && dataItem.latitude && dataItem.longitude) {
                    map.setView([dataItem.latitude, dataItem.longitude], 16);
                    console.log(`自动跳转到位置: ${dataItem.latitude}, ${dataItem.longitude}`);
                }
            }

            updateMapMarkers();
            document.getElementById('selected-count').textContent = selectedItems.size;

            // 更新界面
            const item = document.querySelector(`[data-id="${id}"]`);
            if (item) {
                item.classList.toggle('selected', selectedItems.has(id));
            }

            // 更新按钮状态
            updateButtonStates();
        }

        // 全选/取消全选切换
        function selectAll() {
            const btn = event.target;

            // 如果没有数据，不执行任何操作
            if (inspectionData.length === 0) {
                console.log('没有数据可选择');
                return;
            }

            if (selectedItems.size === inspectionData.length && inspectionData.length > 0) {
                // 当前已全选，执行取消全选
                selectedItems.clear();
                btn.textContent = '全选';
                console.log('已取消全选');
            } else {
                // 执行全选
                inspectionData.forEach(item => selectedItems.add(item.id));
                btn.textContent = '取消全选';
                console.log(`已全选 ${inspectionData.length} 条数据`);
            }

            updateDataList(inspectionData);
            updateMapMarkers();
            document.getElementById('selected-count').textContent = selectedItems.size;
        }

        // 清空选择
        function clearSelection() {
            selectedItems.clear();
            updateDataList(inspectionData);
            updateMapMarkers();
            document.getElementById('selected-count').textContent = 0;

            // 重置按钮文本
            const selectAllBtn = document.querySelector('button[onclick="selectAll()"]');
            if (selectAllBtn) selectAllBtn.textContent = '全选';

            const criticalBtn = document.querySelector('button[onclick="markSelectedAsCritical()"]');
            if (criticalBtn) criticalBtn.textContent = '标记';
        }

        // 刷新数据
        function refreshData() {
            loadInspectionData();
        }

        // 过滤数据
        function filterData() {
            const timeFilter = document.getElementById('time-filter').value;

            // 处理全部数据选项
            if (timeFilter === 'all') {
                isLoadingAll = true;
                currentPage = 1;
                document.getElementById('calendar-controls').style.display = 'none';
                document.getElementById('date-range-display').style.display = 'none';
            } else if (timeFilter === 'custom') {
                isLoadingAll = false;
                document.getElementById('calendar-controls').style.display = 'block';
                document.getElementById('date-range-display').style.display = 'block';

                // 设置默认日期（如果没有设置）
                const startDateInput = document.getElementById('start-date');
                const endDateInput = document.getElementById('end-date');

                if (!startDateInput.value) {
                    const today = new Date();
                    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                    startDateInput.value = lastWeek.toISOString().split('T')[0];
                    endDateInput.value = today.toISOString().split('T')[0];
                }
            } else {
                isLoadingAll = false;
                currentPage = 1;
                document.getElementById('calendar-controls').style.display = 'none';
                document.getElementById('date-range-display').style.display = 'none';
            }

            loadInspectionData();
        }

        // 切换日历显示
        function toggleCalendar() {
            const timeFilter = document.getElementById('time-filter');
            if (timeFilter.value === 'custom') {
                timeFilter.value = '24h';
            } else {
                timeFilter.value = 'custom';
            }
            filterData();
        }

        // 更新日期范围显示
        function updateDateRangeDisplay(startDate, endDate, recordCount) {
            const display = document.getElementById('date-range-display');
            const text = document.getElementById('date-range-text');

            const start = new Date(startDate).toLocaleDateString('zh-CN');
            const end = new Date(endDate).toLocaleDateString('zh-CN');

            if (recordCount !== undefined) {
                text.textContent = `📅 ${start} 至 ${end} (${recordCount} 条记录)`;
            } else {
                text.textContent = `📅 ${start} 至 ${end}`;
            }

            display.style.display = 'block';
        }



        // 跳转到位置
        function jumpToLocation(index) {
            const item = inspectionData[index];
            if (!item || !item.latitude || !item.longitude) return;

            // 高亮当前项
            document.querySelectorAll('.data-item').forEach(el => el.classList.remove('highlighted'));
            document.querySelector(`[data-index="${index}"]`).classList.add('highlighted');

            // 地图跳转并放大到最大级别
            map.setView([item.latitude, item.longitude], 18);

            // 如果有对应的标记，打开弹出窗口
            const marker = markers.find(m => m.options.dataId === item.id);
            if (marker) {
                marker.openPopup();
            }
        }

        // 更新地图标记
        function updateMapMarkers() {
            if (!mapInitialized || !map) return;

            // 清除旧标记
            markers.forEach(marker => map.removeLayer(marker));
            markers = [];

            // 只显示选中且在气体浓度筛选范围内的数据点
            const selectedData = inspectionData.filter(item =>
                selectedItems.has(item.id) && isInGasRange(item)
            );

            if (selectedData.length === 0) {
                console.log('没有符合筛选条件的数据点需要显示');
                return;
            }

            console.log(`显示 ${selectedData.length} 个标记点（已筛选）`);

            const points = [];

            selectedData.forEach(item => {
                if (!item.latitude || !item.longitude) return;

                // 获取所有气体数据
                const allGasData = getAllGasData(item);

                // 如果没有气体数据，使用传统方式
                if (allGasData.length === 0) {
                    const gasValue = getGasValue(item);
                    if (gasValue > 0) {
                        allGasData.push({
                            name: '气体',
                            value: gasValue,
                            unit: 'ppm',
                            thresholds: { warning: 500, danger: 1000 }
                        });
                    }
                }

                // 获取最高危险级别的颜色
                const { color: markerColor, textColor } = getHighestDangerColor(allGasData);

                // 构建显示内容
                let displayContent = '';
                if (allGasData.length === 0) {
                    displayContent = '<div style="font-size: 12px; font-weight: bold;">无数据</div>';
                } else {
                    // 最多显示3个气体，超过的用"..."表示
                    const displayGases = allGasData.slice(0, 3);
                    displayContent = displayGases.map(gas => {
                        let value = gas.value;
                        let displayUnit = gas.unit;

                        // 简化显示数值
                        if (value >= 1000) {
                            value = Math.round(value / 1000) + 'K';
                            displayUnit = 'ppm'; // 保持ppm单位
                        } else {
                            value = Math.round(value);
                        }

                        return `<div style="font-size: 10px; line-height: 1.1; margin: 1px 0; text-align: center; white-space: nowrap;">
                                    ${gas.name}: ${value} ${displayUnit}
                                </div>`;
                    }).join('');

                    // 如果有更多气体，显示省略号
                    if (allGasData.length > 3) {
                        displayContent += '<div style="font-size: 9px; opacity: 0.9; margin-top: 2px; text-align: center;">+' + (allGasData.length - 3) + '种气体</div>';
                    }
                }

                // 计算标记尺寸 - 根据内容动态调整
                const gasCount = Math.max(allGasData.length, 1);
                let markerWidth, markerHeight;

                // 估算最长文本长度
                let maxTextLength = 0;
                if (allGasData.length > 0) {
                    allGasData.slice(0, 3).forEach(gas => {
                        let value = gas.value >= 1000 ? Math.round(gas.value / 1000) + 'K' : Math.round(gas.value);
                        let textLength = (gas.name + ': ' + value + ' ' + gas.unit).length;
                        maxTextLength = Math.max(maxTextLength, textLength);
                    });
                }

                // 根据文本长度和气体数量计算尺寸
                if (gasCount === 1) {
                    markerWidth = Math.max(80, Math.min(maxTextLength * 6 + 20, 120));
                    markerHeight = 32;
                } else if (gasCount === 2) {
                    markerWidth = Math.max(100, Math.min(maxTextLength * 6 + 20, 140));
                    markerHeight = 48;
                } else {
                    markerWidth = Math.max(120, Math.min(maxTextLength * 6 + 20, 160));
                    markerHeight = gasCount > 3 ? 70 : 64;
                }

                // 创建多气体显示的数据框标记
                const customIcon = L.divIcon({
                    className: 'custom-data-marker',
                    html: `
                        <div style="position: relative; width: ${markerWidth}px; height: ${markerHeight + 12}px;">
                            <div style="
                                background: ${markerColor};
                                color: ${textColor};
                                border: 2px solid white;
                                border-radius: 6px;
                                padding: 4px 6px;
                                font-weight: bold;
                                font-size: 10px;
                                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                                text-align: center;
                                line-height: 1.1;
                                width: ${markerWidth}px;
                                height: ${markerHeight}px;
                                position: absolute;
                                top: 0;
                                left: 0;
                                display: flex;
                                flex-direction: column;
                                justify-content: center;
                                align-items: center;
                                box-sizing: border-box;
                                overflow: hidden;
                            ">
                                ${displayContent}
                            </div>
                            <div style="
                                position: absolute;
                                top: ${markerHeight}px;
                                left: 50%;
                                transform: translateX(-50%);
                                width: 0;
                                height: 0;
                                border-left: 6px solid transparent;
                                border-right: 6px solid transparent;
                                border-top: 8px solid ${markerColor};
                                border-bottom: 2px solid white;
                            "></div>
                        </div>
                    `,
                    iconSize: [markerWidth, markerHeight + 12],
                    iconAnchor: [markerWidth / 2, markerHeight + 12]
                });

                const marker = L.marker([item.latitude, item.longitude], {
                    icon: customIcon,
                    dataId: item.id
                }).addTo(map);

                // 创建弹出窗口内容
                const popupContent = createPopupContent(item);
                marker.bindPopup(popupContent);

                markers.push(marker);
                points.push([item.latitude, item.longitude]);
            });

            // 只在首次加载时自动调整地图视野
            if (points.length > 0 && isFirstLoad) {
                const group = new L.featureGroup(markers);
                map.fitBounds(group.getBounds().pad(0.1));
                console.log('首次加载，自动调整地图视野');
                isFirstLoad = false;
            }
        }

        // 创建弹出窗口内容
        function createPopupContent(item) {
            let sensorData = '';
            if (item.sensors && Object.keys(item.sensors).length > 0) {
                sensorData = '<div style="margin-top: 10px;"><strong>传感器数据:</strong><br>';

                // 按类型分组显示传感器数据
                const gasData = [];
                const otherData = [];

                for (const [key, value] of Object.entries(item.sensors)) {
                    const chineseName = gasNameMapping[key.toLowerCase()] || key;
                    const unit = gasUnitMapping[key.toLowerCase()] || '';
                    const displayValue = `${chineseName}: <strong>${value} ${unit}</strong>`;

                    if (isGasType(key)) {
                        gasData.push(displayValue);
                    } else {
                        otherData.push(displayValue);
                    }
                }

                // 先显示气体数据
                if (gasData.length > 0) {
                    sensorData += '<div style="color: #e74c3c; font-weight: bold;">🔥 气体检测:</div>';
                    gasData.forEach(data => {
                        sensorData += `<div style="margin: 2px 0; padding-left: 10px;">${data}</div>`;
                    });
                }

                // 再显示其他传感器数据
                if (otherData.length > 0) {
                    if (gasData.length > 0) {
                        sensorData += '<div style="margin-top: 8px; color: #3498db; font-weight: bold;">📊 环境数据:</div>';
                    }
                    otherData.forEach(data => {
                        sensorData += `<div style="margin: 2px 0; padding-left: 10px;">${data}</div>`;
                    });
                }

                sensorData += '</div>';
            }

            return `
                <div style="min-width: 200px;">
                    <h4 style="margin: 0 0 8px 0; color: #2c3e50;">${item.topic || '巡检设备'}</h4>
                    <p style="margin: 4px 0; color: #7f8c8d;">
                        <strong>位置:</strong> ${item.latitude.toFixed(6)}, ${item.longitude.toFixed(6)}
                    </p>
                    <p style="margin: 4px 0; color: #7f8c8d;">
                        <strong>时间:</strong> ${item.time}
                    </p>
                    ${sensorData}
                </div>
            `;
        }

        // 数据管理
        let messageStatuses = {}; // 存储消息状态
        let originalStatuses = {}; // 存储原始状态，用于恢复
        let allData = []; // 缓存所有数据
        let currentPage = 1; // 当前页码
        let pageSize = 50; // 每页显示数量
        let isLoadingAll = false; // 是否显示全部数据
        let currentSort = 'time_desc'; // 当前排序方式
        let gasFilterRange = 'all'; // 当前气体浓度筛选范围
        let isFirstLoad = true; // 是否首次加载数据

        // 实时刷新相关
        let lastMessageId = 0; // 记录最后一条消息的ID
        let autoRefreshInterval = null; // 定时刷新句柄
        let realTimeCheckInterval = null; // 实时检查句柄
        let isRealTimeEnabled = true; // 是否启用实时刷新

        // 地图状态管理函数
        function saveMapState() {
            if (!mapInitialized || !map) return;

            mapState.center = [map.getCenter().lat, map.getCenter().lng];
            mapState.zoom = map.getZoom();
            mapState.lastSaved = Date.now();

            // 保存到本地存储
            localStorage.setItem('mapState', JSON.stringify(mapState));
            console.log('地图状态已保存:', mapState);
        }

        function loadMapState() {
            try {
                const saved = localStorage.getItem('mapState');
                if (saved) {
                    const savedState = JSON.parse(saved);
                    // 检查保存时间，如果超过1小时则不恢复（避免过期状态）
                    if (savedState.lastSaved && (Date.now() - savedState.lastSaved < 3600000)) {
                        mapState = savedState;
                        console.log('已加载地图状态:', mapState);
                        return true;
                    }
                }
            } catch (error) {
                console.error('加载地图状态失败:', error);
            }
            return false;
        }

        function restoreMapState() {
            if (!mapInitialized || !map) return;

            if (mapState.center && mapState.zoom) {
                map.setView(mapState.center, mapState.zoom);
                console.log('地图状态已恢复:', mapState);
            }
        }

        // 适应视野功能
        function fitMapToMarkers() {
            if (!mapInitialized || !map || markers.length === 0) {
                alert('没有可显示的标记点');
                return;
            }

            const group = new L.featureGroup(markers);
            map.fitBounds(group.getBounds().pad(0.1));
            console.log('地图视野已调整到适应所有标记点');
        }

        // 从本地存储加载状态
        function loadMessageStatuses() {
            try {
                const saved = localStorage.getItem('messageStatuses');
                if (saved) {
                    messageStatuses = JSON.parse(saved);
                    console.log('已加载消息状态:', Object.keys(messageStatuses).length, '条');
                }

                const savedOriginal = localStorage.getItem('originalStatuses');
                if (savedOriginal) {
                    originalStatuses = JSON.parse(savedOriginal);
                    console.log('已加载原始状态:', Object.keys(originalStatuses).length, '条');
                }
            } catch (e) {
                console.warn('加载消息状态失败:', e);
                messageStatuses = {};
                originalStatuses = {};
            }
        }

        // 保存状态到本地存储
        function saveMessageStatuses() {
            try {
                localStorage.setItem('messageStatuses', JSON.stringify(messageStatuses));
                localStorage.setItem('originalStatuses', JSON.stringify(originalStatuses));
            } catch (e) {
                console.warn('保存消息状态失败:', e);
            }
        }

        // 清理过期状态（保留最近7天的状态）
        function cleanupOldStatuses() {
            const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
            let cleaned = 0;

            for (const [id, status] of Object.entries(messageStatuses)) {
                // 如果ID包含时间戳，检查是否过期
                if (id.includes('_')) {
                    const timestamp = parseInt(id.split('_')[0]);
                    if (timestamp && timestamp < sevenDaysAgo) {
                        delete messageStatuses[id];
                        delete originalStatuses[id]; // 同时清理原始状态
                        cleaned++;
                    }
                }
            }

            if (cleaned > 0) {
                console.log(`清理了 ${cleaned} 条过期状态`);
                saveMessageStatuses();
            }
        }

        // 智能状态恢复函数
        function getSmartRestoreStatus(messageId) {
            const originalStatus = originalStatuses[messageId];

            // 如果有明确的原始状态记录且不是critical，使用原始状态
            if (originalStatus && originalStatus !== 'critical') {
                return originalStatus;
            }

            // 否则，由于用户已经查看并操作过这条消息，应该是已读状态
            return 'read';
        }

        function setMessageStatus(messageId, status, event) {
            event.stopPropagation(); // 阻止事件冒泡

            // 如果是严重状态的切换
            if (status === 'critical') {
                const currentStatus = messageStatuses[messageId] || 'new';
                if (currentStatus === 'critical') {
                    // 当前已经是严重状态，切换为取消严重
                    // 简单粗暴：直接设为已读状态
                    messageStatuses[messageId] = 'read';
                    status = 'read'; // 更新要显示的状态
                    console.log(`消息 ${messageId} 取消标记，设为已读`);
                } else {
                    // 当前不是严重状态，记录原始状态并设为严重
                    if (!originalStatuses[messageId]) {
                        originalStatuses[messageId] = currentStatus;
                        console.log(`消息 ${messageId} 记录原始状态: ${currentStatus}`);
                    }
                    messageStatuses[messageId] = 'critical';
                    status = 'critical';
                    console.log(`消息 ${messageId} 设置为标记状态`);
                }
            } else {
                // 非严重状态的正常设置
                // 如果当前是严重状态，需要更新原始状态记录
                const currentStatus = messageStatuses[messageId] || 'new';
                if (currentStatus === 'critical') {
                    // 从严重状态切换到其他状态，更新原始状态记录
                    originalStatuses[messageId] = status;
                } else if (!originalStatuses[messageId]) {
                    // 如果没有原始状态记录，设置当前状态为原始状态
                    originalStatuses[messageId] = currentStatus;
                }
                messageStatuses[messageId] = status;
                console.log(`消息 ${messageId} 状态设置为: ${status}`);
            }

            // 保存到本地存储
            saveMessageStatuses();

            // 更新UI
            const dataItem = document.querySelector(`[data-id="${messageId}"]`);
            if (dataItem) {
                // 移除所有状态类
                dataItem.classList.remove('status-new', 'status-critical', 'status-read');
                // 添加新状态类
                dataItem.classList.add(`status-${status}`);
                // 更新数据属性
                dataItem.setAttribute('data-status', status);

                // 更新标记按钮的文字
                const criticalBtn = dataItem.querySelector('.status-btn.critical');
                if (criticalBtn) {
                    if (status === 'critical') {
                        criticalBtn.textContent = '取消';
                    } else {
                        criticalBtn.textContent = '标记';
                    }
                }
            }

            console.log(`消息 ${messageId} 状态更新为: ${status}`);
        }

        // 批量状态操作
        function markAllAsRead() {
            document.querySelectorAll('.data-item.status-new').forEach(item => {
                const messageId = item.getAttribute('data-id');
                setMessageStatus(messageId, 'read', {stopPropagation: () => {}});
            });
            console.log('所有新消息已标记为已读');
        }

        function markSelectedAsCritical() {
            if (selectedItems.size === 0) {
                alert('请先选择要操作的数据');
                return;
            }

            const btn = event.target;

            // 检查选中的消息是否都已经是严重状态
            const selectedArray = Array.from(selectedItems);
            const allCritical = selectedArray.every(id => {
                const currentStatus = messageStatuses[id] || 'new';
                return currentStatus === 'critical';
            });

            if (allCritical) {
                // 取消严重状态，恢复到之前的状态
                selectedItems.forEach(id => {
                    // 简单粗暴：直接设为已读状态
                    const restoreStatus = 'read';

                    // 直接更新状态和UI
                    messageStatuses[id] = restoreStatus;

                    const dataItem = document.querySelector(`[data-id="${id}"]`);
                    if (dataItem) {
                        dataItem.classList.remove('status-new', 'status-critical', 'status-read');
                        dataItem.classList.add(`status-${restoreStatus}`);
                        dataItem.setAttribute('data-status', restoreStatus);

                        // 更新标记按钮文字
                        const criticalBtn = dataItem.querySelector('.status-btn.critical');
                        if (criticalBtn) {
                            criticalBtn.textContent = '标记';
                        }
                    }

                    console.log(`消息 ${id} 取消严重，恢复为: ${restoreStatus}`);
                });

                saveMessageStatuses();
                btn.textContent = '标记';
                console.log(`${selectedItems.size} 条消息已取消标记状态`);
            } else {
                // 标记为严重
                selectedItems.forEach(id => {
                    const currentStatus = messageStatuses[id] || 'new';
                    if (!originalStatuses[id]) {
                        originalStatuses[id] = currentStatus;
                    }

                    messageStatuses[id] = 'critical';

                    const dataItem = document.querySelector(`[data-id="${id}"]`);
                    if (dataItem) {
                        dataItem.classList.remove('status-new', 'status-critical', 'status-read');
                        dataItem.classList.add('status-critical');
                        dataItem.setAttribute('data-status', 'critical');

                        // 更新标记按钮文字
                        const criticalBtn = dataItem.querySelector('.status-btn.critical');
                        if (criticalBtn) {
                            criticalBtn.textContent = '取消';
                        }
                    }
                });

                saveMessageStatuses();
                btn.textContent = '取消';
                console.log(`${selectedItems.size} 条消息已标记`);
            }
        }

        // 调试状态信息
        function debugStatuses() {
            console.log('=== 状态调试信息 ===');
            console.log('当前状态记录:', messageStatuses);
            console.log('原始状态记录:', originalStatuses);

            if (selectedItems.size > 0) {
                console.log('选中消息状态:');
                selectedItems.forEach(id => {
                    const current = messageStatuses[id] || 'new';
                    const original = originalStatuses[id] || 'new';
                    console.log(`ID ${id}: 当前=${current}, 原始=${original}`);
                });
            }

            alert('状态调试信息已输出到控制台，请按F12查看');
        }

        // 排序功能
        function applySorting() {
            currentSort = document.getElementById('sort-select').value;
            console.log(`应用排序: ${currentSort}`);
            sortData(allData);
            updateDataList(allData);
        }

        function sortData(data) {
            data.sort((a, b) => {
                switch(currentSort) {
                    case 'time_desc':
                        return new Date(b.time) - new Date(a.time);
                    case 'time_asc':
                        return new Date(a.time) - new Date(b.time);
                    case 'gas_desc':
                        const gasB = getGasValue(b);
                        const gasA = getGasValue(a);
                        console.log(`排序比较: ${gasB} vs ${gasA}`);
                        return gasB - gasA;
                    case 'gas_asc':
                        return getGasValue(a) - getGasValue(b);
                    default:
                        return 0;
                }
            });

            if (currentSort.includes('gas')) {
                console.log('气体浓度排序结果:', data.slice(0, 3).map(item => ({
                    id: item.id,
                    gas: getGasValue(item),
                    sensors: item.sensors
                })));
            }
        }

        function getGasValue(item) {
            if (!item.sensors) return 0;

            // 尝试多种可能的气体浓度字段名（按优先级排序）
            const gasFields = [
                'g', 'gas', 'gas_concentration', 'concentration',
                'ch4', 'h2', 'co', 'co2', 'h2s', 'sf6', 'nh3', 'so2', 'no2', 'o3',
                'c2h6', 'c3h8', 'c4h10', 'voc'
            ];

            for (const field of gasFields) {
                if (item.sensors[field] !== undefined) {
                    const value = parseFloat(item.sensors[field]);
                    if (!isNaN(value)) {
                        return value;
                    }
                }
            }

            // 如果都没找到，尝试从传感器数据中查找包含气体相关关键词的字段
            for (const [key, value] of Object.entries(item.sensors)) {
                const keyLower = key.toLowerCase();
                if (keyLower.includes('gas') || keyLower.includes('浓度') ||
                    keyLower.includes('concentration') || keyLower.includes('ppm')) {
                    const numValue = parseFloat(value);
                    if (!isNaN(numValue)) {
                        return numValue;
                    }
                }
            }

            return 0;
        }

        // 获取主要气体类型和值（用于显示）
        function getPrimaryGasInfo(item) {
            if (!item.sensors) return { type: '未知', value: 0, unit: 'ppm' };

            // 按优先级查找气体类型
            const gasFields = [
                'ch4', 'h2', 'co', 'h2s', 'sf6', 'co2', 'nh3', 'so2', 'no2', 'o3',
                'c2h6', 'c3h8', 'c4h10', 'voc', 'g', 'gas', 'gas_concentration'
            ];

            for (const field of gasFields) {
                if (item.sensors[field] !== undefined) {
                    const value = parseFloat(item.sensors[field]);
                    if (!isNaN(value)) {
                        return {
                            type: gasNameMapping[field] || field,
                            value: value,
                            unit: gasUnitMapping[field] || 'ppm'
                        };
                    }
                }
            }

            return { type: '气体浓度', value: 0, unit: 'ppm' };
        }

        // 获取所有气体数据（用于地图标记显示）
        function getAllGasData(item) {
            if (!item.sensors) return [];

            const gasData = [];
            const gasFields = [
                'ch4', 'h2', 'co', 'h2s', 'sf6', 'co2', 'nh3', 'so2', 'no2', 'o2', 'o3',
                'c2h6', 'c3h8', 'c4h10', 'voc', 'g', 'gas', 'gas_concentration'
            ];

            // 收集所有气体数据
            for (const [key, value] of Object.entries(item.sensors)) {
                const keyLower = key.toLowerCase();
                if (isGasType(key)) {
                    const numValue = parseFloat(value);
                    if (!isNaN(numValue) && numValue > 0) {
                        gasData.push({
                            key: key,
                            name: gasNameMapping[keyLower] || key.toUpperCase(),
                            value: numValue,
                            unit: gasUnitMapping[keyLower] || 'ppm',
                            thresholds: getGasThresholds(key)
                        });
                    }
                }
            }

            // 按危险程度排序（危险的在前面）
            gasData.sort((a, b) => {
                const aDanger = a.value > a.thresholds.danger ? 3 : (a.value > a.thresholds.warning ? 2 : 1);
                const bDanger = b.value > b.thresholds.danger ? 3 : (b.value > b.thresholds.warning ? 2 : 1);
                return bDanger - aDanger;
            });

            return gasData;
        }

        // 获取最高危险级别的颜色
        function getHighestDangerColor(gasDataArray) {
            let maxDangerLevel = 0;
            let color = '#28a745'; // 绿色 - 正常
            let textColor = 'white';

            for (const gas of gasDataArray) {
                let dangerLevel = 0;
                if (gas.value > gas.thresholds.danger) {
                    dangerLevel = 3;
                } else if (gas.value > gas.thresholds.warning) {
                    dangerLevel = 2;
                } else {
                    dangerLevel = 1;
                }

                if (dangerLevel > maxDangerLevel) {
                    maxDangerLevel = dangerLevel;
                    if (dangerLevel === 3) {
                        color = '#dc3545'; // 红色 - 危险
                        textColor = 'white';
                    } else if (dangerLevel === 2) {
                        color = '#ffc107'; // 黄色 - 警告
                        textColor = 'black';
                    }
                }
            }

            return { color, textColor };
        }

        // 气体浓度筛选功能
        function applyGasFilter() {
            gasFilterRange = document.getElementById('gas-filter').value;

            // 显示/隐藏自定义范围输入框
            const customInputs = document.querySelectorAll('#gas-min, #gas-max');
            if (gasFilterRange === 'custom') {
                customInputs.forEach(input => input.style.display = 'inline-block');
            } else {
                customInputs.forEach(input => input.style.display = 'none');
            }

            // 应用筛选并更新地图
            updateMapMarkers();
            console.log(`应用气体浓度筛选: ${gasFilterRange}`);
        }

        // 检查数据是否在气体浓度筛选范围内
        function isInGasRange(item) {
            const gasValue = getGasValue(item);

            switch(gasFilterRange) {
                case 'all':
                    return true;
                case '0-500':
                    return gasValue >= 0 && gasValue <= 500;
                case '500-2000':
                    return gasValue > 500 && gasValue <= 2000;
                case '2000-999999':
                    return gasValue > 2000;
                case 'custom':
                    const minValue = parseFloat(document.getElementById('gas-min').value) || 0;
                    const maxValue = parseFloat(document.getElementById('gas-max').value) || 999999;
                    return gasValue >= minValue && gasValue <= maxValue;
                default:
                    return true;
            }
        }



        // 删除消息
        function deleteMessage(messageId, event) {
            event.stopPropagation();

            if (!confirm('确定要删除这条数据吗？')) {
                return;
            }

            fetch(`/api/delete-message/${messageId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    // 移除选择状态
                    selectedItems.delete(messageId);

                    // 移除状态记录
                    delete messageStatuses[messageId];
                    delete originalStatuses[messageId];
                    saveMessageStatuses();

                    // 重新加载数据以确保统计准确
                    loadInspectionData();

                    console.log(`消息 ${messageId} 已删除`);
                } else {
                    alert('删除失败: ' + result.message);
                }
            })
            .catch(error => {
                console.error('删除失败:', error);
                alert('删除失败，请重试');
            });
        }

        // 批量删除选中的消息
        function deleteSelected() {
            if (selectedItems.size === 0) {
                alert('请先选择要删除的数据');
                return;
            }

            if (!confirm(`确定要删除选中的 ${selectedItems.size} 条数据吗？`)) {
                return;
            }

            const idsToDelete = Array.from(selectedItems);

            fetch('/api/delete-messages', {
                method: 'DELETE',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({ids: idsToDelete})
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    // 🔥 调试信息：显示删除操作的详细结果
                    console.log('删除操作结果:', result);
                    if (result.debug) {
                        console.log(`数据库记录变化: ${result.debug.count_before} → ${result.debug.count_after}`);
                        console.log(`实际删除: ${result.debug.deleted_count} 条`);
                    }

                    // 清空选择
                    selectedItems.clear();

                    // 移除状态记录
                    idsToDelete.forEach(id => {
                        delete messageStatuses[id];
                        delete originalStatuses[id];
                    });
                    saveMessageStatuses();

                    // 重新加载数据以确保统计准确
                    loadInspectionData();

                    console.log(`已删除 ${idsToDelete.length} 条消息`);
                } else {
                    alert('批量删除失败: ' + result.message);
                }
            })
            .catch(error => {
                console.error('批量删除失败:', error);
                alert('批量删除失败，请重试');
            });
        }

        // 页面加载完成后初始化
        window.onload = function() {
            console.log('页面加载完成，初始化地图...');

            // 加载消息状态
            loadMessageStatuses();

            // 清理过期状态
            cleanupOldStatuses();

            // 初始化地图
            initMap();

            // 启动定时刷新（30秒）
            autoRefreshInterval = setInterval(loadInspectionData, 30000);

            // 启动实时检查新消息（5秒）
            realTimeCheckInterval = setInterval(checkNewMessages, 5000);

            // 为自定义浓度范围输入框添加事件监听
            document.getElementById('gas-min').addEventListener('input', applyGasFilter);
            document.getElementById('gas-max').addEventListener('input', applyGasFilter);
        };
    </script>
</body>
</html>
    ''')

@app.route('/api/inspection-data')
def get_inspection_data():
    """获取巡检数据API"""
    try:
        time_filter = request.args.get('time_filter', '24h')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        since_id = request.args.get('since_id')  # 新增：获取新消息的参数

        # 分页参数
        page = int(request.args.get('page', 1))
        limit = request.args.get('limit', '50')

        # 处理显示全部的情况
        if limit == 'all':
            limit_num = 10000  # 设置一个较大的数字
            offset = 0
        else:
            limit_num = int(limit)
            offset = (page - 1) * limit_num

        # 计算时间范围 - 修复时区问题
        # 使用UTC时间来匹配数据库中的时间
        from datetime import timezone
        now_utc = datetime.now(timezone.utc).replace(tzinfo=None)  # 转换为UTC时间但移除时区信息

        # 对于本地时间，我们需要减去8小时来匹配UTC时间
        now_local = datetime.now()
        utc_offset = timedelta(hours=8)  # 中国时区UTC+8

        if time_filter == 'custom' and start_date and end_date:
            # 自定义日期范围 - 假设用户输入的是本地时间，需要转换为UTC
            start_time_local = datetime.strptime(start_date, '%Y-%m-%d')
            end_time_local = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1) - timedelta(seconds=1)
            start_time = start_time_local - utc_offset
            end_time = end_time_local - utc_offset
        elif time_filter == '1h':
            start_time = now_utc - timedelta(hours=1)
            end_time = now_utc
        elif time_filter == '6h':
            start_time = now_utc - timedelta(hours=6)
            end_time = now_utc
        elif time_filter == '24h':
            start_time = now_utc - timedelta(hours=24)
            end_time = now_utc
        elif time_filter == '7d':
            start_time = now_utc - timedelta(days=7)
            end_time = now_utc
        elif time_filter == '30d':
            start_time = now_utc - timedelta(days=30)
            end_time = now_utc
        else:  # all
            start_time = datetime(2020, 1, 1)
            end_time = now_utc

        # 调试信息
        logger.info(f"时间过滤器: {time_filter}")
        logger.info(f"本地时间: {now_local}")
        logger.info(f"UTC时间: {now_utc}")
        logger.info(f"查询时间范围: {start_time} 到 {end_time}")

        conn = get_db_connection()
        if not conn:
            return jsonify({"status": "error", "message": "数据库连接失败"}), 500

        cursor = conn.cursor(dictionary=True)

        # 如果是检查新消息（since_id参数存在）
        if since_id:
            query = """
                SELECT id, client_id, topic, payload, arrived
                FROM mqtt_messages
                WHERE id > %s
                ORDER BY arrived DESC
                LIMIT %s
            """
            cursor.execute(query, (int(since_id), limit_num))
            raw_data = cursor.fetchall()

            # 简单统计
            stats = {'total': len(raw_data)}
        # 查询巡检数据（支持分页）
        elif time_filter == 'custom':
            query = """
                SELECT id, client_id, topic, payload, arrived
                FROM mqtt_messages
                WHERE arrived >= %s AND arrived <= %s
                ORDER BY arrived DESC
                LIMIT %s OFFSET %s
            """
            cursor.execute(query, (start_time, end_time, limit_num, offset))
            raw_data = cursor.fetchall()

            # 获取统计信息 - 使用新的cursor
            cursor.close()
            cursor = conn.cursor(dictionary=True)
            cursor.execute("SELECT COUNT(*) as total FROM mqtt_messages WHERE arrived >= %s AND arrived <= %s",
                         (start_time, end_time))
            stats = cursor.fetchone()
        else:
            query = """
                SELECT id, client_id, topic, payload, arrived
                FROM mqtt_messages
                WHERE arrived >= %s
                ORDER BY arrived DESC
                LIMIT %s OFFSET %s
            """
            cursor.execute(query, (start_time, limit_num, offset))
            raw_data = cursor.fetchall()

            # 获取统计信息 - 使用新的cursor
            cursor.close()
            cursor = conn.cursor(dictionary=True)
            cursor.execute("SELECT COUNT(*) as total FROM mqtt_messages WHERE arrived >= %s", (start_time,))
            stats = cursor.fetchone()

        cursor.close()
        conn.close()

        # 处理数据
        processed_data = []
        for row in raw_data:
            sensor_data = parse_sensor_data(row['payload'])

            if sensor_data and sensor_data['latitude'] and sensor_data['longitude']:
                processed_data.append({
                    'id': row['id'],
                    'client_id': row['client_id'],
                    'topic': row['topic'],
                    'latitude': sensor_data['latitude'],
                    'longitude': sensor_data['longitude'],
                    'sensors': sensor_data['sensors'],
                    'time': row['arrived'].strftime('%Y-%m-%d %H:%M:%S'),
                    'timestamp': sensor_data['timestamp']
                })

        # 构建返回数据
        result = {
            "status": "success",
            "data": processed_data,
            "stats": {
                "total": stats['total'],
                "filtered": len(processed_data)
            }
        }

        # 如果是自定义日期查询，添加日期范围信息
        if time_filter == 'custom' and start_date and end_date:
            result["stats"]["date_range"] = {
                "start": start_date,
                "end": end_date
            }

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取巡检数据失败: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/test-data', methods=['POST'])
def add_test_data():
    """添加测试数据"""
    try:
        # 模拟天然气管道巡检数据 - 使用精简格式
        timestamp = int(datetime.now().timestamp())
        test_data = [
            {
                "client_id": "inspector_001",
                "topic": "pipeline/section_a",
                "payload": {
                    "lat": 39.9042,
                    "lng": 116.4074,
                    "t": 25.6,
                    "p": 2.5,
                    "g": 120,
                    "ts": timestamp
                }
            },
            {
                "client_id": "inspector_002",
                "topic": "pipeline/section_b",
                "payload": [39.9163, 116.3972, 28.3, 2.8, 850, 65, timestamp]  # 数组格式
            },
            {
                "client_id": "inspector_003",
                "topic": "pipeline/section_c",
                "payload": "39.9075,116.3974,32.1,3.2,1200,45"  # 字符串格式（最省流量）
            },
            {
                "client_id": "inspector_004",
                "topic": "pipeline/section_d",
                "payload": {
                    "lat": 39.9200,
                    "lng": 116.4100,
                    "t": 29.8,
                    "p": 2.9,
                    "g": 650,  # 警告级别
                    "h": 58,
                    "co": 12,
                    "ts": timestamp
                }
            }
        ]

        conn = get_db_connection()
        if not conn:
            return jsonify({"status": "error", "message": "数据库连接失败"}), 500

        cursor = conn.cursor()

        for data in test_data:
            insert_sql = """
                INSERT INTO mqtt_messages (client_id, topic, payload, qos, arrived)
                VALUES (%s, %s, %s, %s, %s)
            """

            values = (
                data['client_id'],
                data['topic'],
                json.dumps(data['payload']),
                0,
                datetime.now()
            )

            cursor.execute(insert_sql, values)

        conn.commit()
        cursor.close()
        conn.close()

        return jsonify({"status": "success", "message": f"添加了 {len(test_data)} 条测试数据"})

    except Exception as e:
        logger.error(f"添加测试数据失败: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/delete-message/<int:message_id>', methods=['DELETE'])
def delete_message(message_id):
    """删除单条消息"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({"status": "error", "message": "数据库连接失败"}), 500

        cursor = conn.cursor()
        cursor.execute("DELETE FROM mqtt_messages WHERE id = %s", (message_id,))

        if cursor.rowcount > 0:
            conn.commit()
            cursor.close()
            conn.close()
            logger.info(f"删除消息成功: {message_id}")
            return jsonify({"status": "success", "message": "删除成功"})
        else:
            cursor.close()
            conn.close()
            return jsonify({"status": "error", "message": "消息不存在"}), 404

    except Exception as e:
        logger.error(f"删除消息失败: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/delete-messages', methods=['DELETE'])
def delete_messages():
    """批量删除消息"""
    try:
        data = request.get_json()
        if not data or 'ids' not in data:
            return jsonify({"status": "error", "message": "缺少消息ID列表"}), 400

        ids = data['ids']
        if not ids:
            return jsonify({"status": "error", "message": "消息ID列表为空"}), 400

        conn = get_db_connection()
        if not conn:
            return jsonify({"status": "error", "message": "数据库连接失败"}), 500

        cursor = conn.cursor()

        # 批量删除
        placeholders = ','.join(['%s'] * len(ids))

        # 调试：删除前检查数据库中的记录数
        cursor.execute("SELECT COUNT(*) FROM mqtt_messages")
        count_before = cursor.fetchone()[0]
        logger.info(f"删除前数据库记录数: {count_before}")

        cursor.execute(f"DELETE FROM mqtt_messages WHERE id IN ({placeholders})", ids)
        deleted_count = cursor.rowcount
        conn.commit()

        # 调试：删除后检查数据库中的记录数
        cursor.execute("SELECT COUNT(*) FROM mqtt_messages")
        count_after = cursor.fetchone()[0]
        logger.info(f"删除后数据库记录数: {count_after}")

        cursor.close()
        conn.close()

        logger.info(f"批量删除消息成功: {deleted_count} 条，数据库记录从 {count_before} 变为 {count_after}")
        return jsonify({
            "status": "success",
            "message": f"成功删除 {deleted_count} 条消息",
            "debug": {
                "count_before": count_before,
                "count_after": count_after,
                "deleted_count": deleted_count
            }
        })

    except Exception as e:
        logger.error(f"批量删除消息失败: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/max-message-id')
def get_max_message_id():
    """获取最大消息ID"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({"status": "error", "message": "数据库连接失败"}), 500

        cursor = conn.cursor()
        cursor.execute("SELECT MAX(id) as max_id FROM mqtt_messages")
        result = cursor.fetchone()

        cursor.close()
        conn.close()

        max_id = result[0] if result and result[0] else 0

        return jsonify({
            "status": "success",
            "max_id": max_id
        })

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/stats')
def get_stats():
    """获取准确的统计数据"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({"status": "error", "message": "数据库连接失败"}), 500

        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) as total FROM mqtt_messages")
        result = cursor.fetchone()

        cursor.close()
        conn.close()

        total = result[0] if result else 0
        return jsonify({
            "status": "success",
            "stats": {"total": total}
        })

    except Exception as e:
        logger.error(f"获取统计数据失败: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8082, debug=True)
