#!/usr/bin/env python3
"""
地图标记修复测试脚本
测试修复后的多气体标记显示和定位
"""

import json
import time
import requests
from datetime import datetime

# 简化的测试数据
test_cases = [
    {
        "name": "单气体测试",
        "device_id": "single_test",
        "lat": 39.920,
        "lng": 116.410,
        "data": {
            "H2": 23730,  # 氢气
            "temp": 28.5
        },
        "expected": "氢气: 24K ppm"
    },
    {
        "name": "双气体测试",
        "device_id": "dual_test", 
        "lat": 39.925,
        "lng": 116.415,
        "data": {
            "H2": 15000,   # 氢气
            "SF6": 1200,   # 六氟化硫
            "temp": 26.8
        },
        "expected": "氢气: 15K ppm\nSF6: 1K ppm"
    },
    {
        "name": "三气体测试",
        "device_id": "triple_test",
        "lat": 39.915,
        "lng": 116.405,
        "data": {
            "CH4": 2500,   # 甲烷
            "CO": 85,      # 一氧化碳
            "H2S": 15,     # 硫化氢
            "temp": 31.2
        },
        "expected": "甲烷: 3K ppm\n一氧化碳: 85 ppm\n硫化氢: 15 ppm"
    }
]

def send_test_data(server_ip="localhost", port=5000):
    """发送测试数据"""
    url = f"http://{server_ip}:{port}/mqtt/data"
    
    print("🔧 地图标记修复测试")
    print("=" * 50)
    print("修复内容:")
    print("1. 标记显示格式优化")
    print("2. 指示小尖角位置修正")
    print("3. 标记大小自适应")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📡 发送测试 {i}/3: {test_case['name']}")
        
        # 构造测试数据
        test_data = {
            "lat": test_case["lat"],
            "lng": test_case["lng"],
            "device_id": test_case["device_id"],
            "name": test_case["name"],
            "timestamp": datetime.now().isoformat()
        }
        test_data.update(test_case["data"])
        
        # 构造EMQX格式
        mqtt_data = {
            "client_id": test_case["device_id"],
            "topic": f"location/{test_case['device_id']}",
            "payload": json.dumps(test_data),
            "qos": 0,
            "retain": False,
            "timestamp": test_data["timestamp"]
        }
        
        try:
            response = requests.post(url, json=mqtt_data, timeout=10)
            if response.status_code == 200:
                print(f"✅ 数据发送成功")
                print(f"   预期显示: {test_case['expected']}")
                
                # 显示气体信息
                gas_info = []
                for key, value in test_case["data"].items():
                    if key.upper() in ['H2', 'SF6', 'CH4', 'CO', 'H2S', 'CO2']:
                        gas_info.append(f"{key}: {value}")
                
                if gas_info:
                    print(f"   包含气体: {', '.join(gas_info)}")
                    
            else:
                print(f"❌ 数据发送失败: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")
        
        # 间隔发送
        if i < len(test_cases):
            time.sleep(2)
    
    print("\n" + "=" * 50)
    print("✅ 测试数据发送完成!")
    
    print("\n📋 验证检查项:")
    print("1. 访问监控界面: http://服务器IP:8082")
    print("2. 查看地图上的3个测试标记")
    print("3. 验证修复效果:")
    print("   ✓ 标记内容格式正确 (气体名: 数值 单位)")
    print("   ✓ 指示小尖角位置居中对准")
    print("   ✓ 标记大小适应内容")
    print("   ✓ 文字清晰可读")
    print("4. 点击标记查看弹出信息")

def check_server_status(server_ip="localhost", port=5000):
    """检查服务器状态"""
    try:
        response = requests.get(f"http://{server_ip}:{port}/health", timeout=5)
        if response.status_code == 200:
            print("✅ MQTT接收服务运行正常")
            return True
        else:
            print(f"❌ 服务状态异常: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务: {e}")
        return False

def main():
    print("🚀 地图标记修复测试工具")
    print("目标: 修复标记显示格式和定位问题")
    print("=" * 50)
    
    # 获取服务器IP
    server_ip = input("请输入服务器IP (默认localhost): ").strip()
    if not server_ip:
        server_ip = "localhost"
    
    print(f"\n🔍 检查服务器状态: {server_ip}:5000")
    if not check_server_status(server_ip):
        print("请确保MQTT接收服务正在运行!")
        return
    
    print(f"\n📡 准备发送3个测试用例到: {server_ip}:5000")
    confirm = input("确认开始测试? (y/N): ").strip().lower()
    
    if confirm == 'y':
        send_test_data(server_ip)
        
        print("\n🎯 修复验证要点:")
        print("- 单气体: 应显示 '氢气: 24K ppm' (一行)")
        print("- 双气体: 应显示两行气体信息")
        print("- 三气体: 应显示三行气体信息")
        print("- 小尖角: 应该指向标记底部中心")
        print("- 标记: 应该大小适中，内容清晰")
    else:
        print("测试已取消")

if __name__ == "__main__":
    main()
