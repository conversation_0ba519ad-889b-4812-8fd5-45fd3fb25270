# 多气体检测功能使用指南

## 🔬 功能概述

天然气管道巡检监控系统 v2.0.0 新增了强大的多气体检测功能，支持15+种常用工业气体的实时监测、智能识别和危险预警。

### ✨ 核心特性
- 🌟 **多气体同时检测** - 单个设备可同时监测多种气体
- 🇨🇳 **中文智能显示** - 自动识别气体类型并显示中文名称
- ⚠️ **专业危险阈值** - 基于工业安全标准的气体危险等级
- 🎨 **颜色预警系统** - 绿色(安全)、黄色(警告)、红色(危险)
- 📊 **单位自适应** - 根据气体类型自动显示正确单位

## 🧪 支持的气体类型

### 🔥 易燃易爆气体
| 气体 | 字段名 | 中文名称 | 单位 | 警告阈值 | 危险阈值 | 应用场景 |
|------|--------|----------|------|----------|----------|----------|
| CH4 | `CH4`, `ch4` | 甲烷 | ppm | 1000 | 5000 | 天然气管道、煤矿 |
| H2 | `H2`, `h2` | 氢气 | ppm | 1000 | 4000 | 化工厂、加氢站 |
| C2H6 | `C2H6`, `c2h6` | 乙烷 | ppm | 1000 | 3000 | 石化工业 |
| C3H8 | `C3H8`, `c3h8` | 丙烷 | ppm | 1000 | 2000 | LPG储罐 |
| C4H10 | `C4H10`, `c4h10` | 丁烷 | ppm | 800 | 1600 | 打火机燃料 |

### ☠️ 有毒有害气体
| 气体 | 字段名 | 中文名称 | 单位 | 警告阈值 | 危险阈值 | 毒性说明 |
|------|--------|----------|------|----------|----------|----------|
| CO | `CO`, `co` | 一氧化碳 | ppm | 50 | 100 | 无色无味，致命毒气 |
| H2S | `H2S`, `h2s` | 硫化氢 | ppm | 10 | 20 | 臭鸡蛋味，剧毒 |
| NH3 | `NH3`, `nh3` | 氨气 | ppm | 25 | 50 | 刺激性气味，腐蚀性 |
| SO2 | `SO2`, `so2` | 二氧化硫 | ppm | 5 | 10 | 刺激呼吸道 |
| NO2 | `NO2`, `no2` | 二氧化氮 | ppm | 3 | 5 | 棕红色，强氧化性 |

### 🏭 工业特殊气体
| 气体 | 字段名 | 中文名称 | 单位 | 警告阈值 | 危险阈值 | 用途 |
|------|--------|----------|------|----------|----------|------|
| SF6 | `SF6`, `sf6` | 六氟化硫 | ppm | 1000 | 5000 | 电力设备绝缘 |
| CO2 | `CO2`, `co2` | 二氧化碳 | ppm | 5000 | 10000 | 窒息性气体 |
| O2 | `O2`, `o2` | 氧气 | % | 16 | 12 | 缺氧危险 |
| O3 | `O3`, `o3` | 臭氧 | ppm | 0.1 | 0.2 | 强氧化性 |
| VOC | `VOC`, `voc` | 挥发性有机物 | ppm | 300 | 1000 | 有机污染物 |

## 📡 数据格式示例

### 基础格式 (您的氢气格式)
```json
{
    "lat": 39.920,
    "lng": 116.410,
    "H2": 23730,              // 氢气浓度 - 您的格式 ✅
    "SF6": 1200,              // 六氟化硫 - 新增支持 ✅
    "temp": 28.0,
    "device_id": "detector_001"
}
```

### 多气体同时检测
```json
{
    "lat": 39.925,
    "lng": 116.415,
    "CH4": 2500,              // 甲烷
    "H2": 23730,              // 氢气
    "CO": 15,                 // 一氧化碳
    "H2S": 8,                 // 硫化氢
    "SF6": 1200,              // 六氟化硫
    "CO2": 800,               // 二氧化碳
    "temp": 26.8,
    "humidity": 58,
    "pressure": 1.01,
    "device_id": "multi_detector_001"
}
```

### 工业环境监测
```json
{
    "lat": 39.915,
    "lng": 116.405,
    "NH3": 25,                // 氨气
    "SO2": 5,                 // 二氧化硫
    "NO2": 3,                 // 二氧化氮
    "VOC": 300,               // 挥发性有机物
    "O2": 20.9,               // 氧气
    "pm25": 45,               // PM2.5
    "pm10": 78,               // PM10
    "noise": 65,              // 噪声
    "device_id": "industrial_monitor_001"
}
```

## 🎨 界面显示效果

### 地图标记颜色
- 🟢 **绿色**: 所有气体浓度正常
- 🟡 **黄色**: 有气体达到警告级别
- 🔴 **红色**: 有气体达到危险级别

### 传感器数据显示
```
🔥 气体检测:
  氢气: 23730 ppm        [红色 - 危险]
  六氟化硫: 1200 ppm     [黄色 - 警告]
  甲烷: 500 ppm          [绿色 - 正常]

📊 环境数据:
  温度: 28.5 °C
  湿度: 65 %
  压力: 1.02 MPa
```

### 弹出窗口信息
点击地图标记时显示：
- 设备名称和位置
- 气体检测数据（按危险级别排序）
- 环境传感器数据
- 时间戳信息

## 🧪 测试验证

### 使用测试脚本
```bash
# 运行多气体检测测试
python3 test_gas_detection.py

# 按提示输入服务器IP
# 确认发送测试数据
```

### 手动测试数据
```bash
# 使用mosquitto客户端发送
mosquitto_pub -h localhost -t "location/gas_test" -m '{
  "lat": 39.920,
  "lng": 116.410,
  "H2": 23730,
  "SF6": 1200,
  "CH4": 2500,
  "device_id": "test_device"
}'
```

### 验证检查项
1. ✅ **数据接收** - 检查数据库是否有新记录
2. ✅ **中文显示** - 验证气体名称显示为中文
3. ✅ **单位正确** - 检查单位显示是否正确
4. ✅ **颜色预警** - 验证危险级别颜色标识
5. ✅ **阈值判断** - 测试不同浓度的预警效果

## ⚠️ 安全阈值说明

### 危险级别定义
- **正常**: 浓度在安全范围内
- **警告**: 浓度超过职业暴露限值
- **危险**: 浓度达到立即危险水平

### 特殊气体说明
- **氧气**: 低于16%为缺氧，低于12%为严重缺氧
- **硫化氢**: 极低浓度即有毒，20ppm为危险级别
- **一氧化碳**: 无色无味，100ppm以上危险
- **六氟化硫**: 电力行业常用，高浓度时窒息性

## 🔧 配置自定义

### 添加新气体类型
在 `gas_pipeline_inspection.py` 中修改：

```javascript
// 1. 添加到传感器映射
sensor_mapping = {
    'new_gas': 'new_gas',  // 新气体字段
    // ... 其他气体
};

// 2. 添加中文名称
gasNameMapping = {
    'new_gas': '新气体名称',
    // ... 其他映射
};

// 3. 添加单位
gasUnitMapping = {
    'new_gas': 'ppm',  // 或其他单位
    // ... 其他单位
};

// 4. 添加阈值
function getGasThresholds(key) {
    const thresholds = {
        'new_gas': { warning: 100, danger: 500 },
        // ... 其他阈值
    };
}
```

## 📞 技术支持

如有问题，请检查：
1. 数据格式是否正确
2. 字段名是否在支持列表中
3. 服务是否正常运行
4. 网络连接是否正常

---

**版本**: v2.0.0  
**更新日期**: 2025-08-13  
**支持气体**: 15+ 种工业常用气体  
**技术支持**: BRIDGES
