#!/usr/bin/env python3
# Version: 1.0 | Status: Active | Description: 简化的服务启动脚本
"""
简化的服务启动脚本
只包含核心功能，用于生产环境
"""

import subprocess
import time
import requests
from datetime import datetime

def start_service(script_name, port, service_name):
    """启动单个服务"""
    print(f"🚀 启动 {service_name}...")
    
    # 停止现有进程
    subprocess.run(f"pkill -f 'python.*{script_name}'", shell=True, capture_output=True)
    time.sleep(2)
    
    # 启动新进程
    try:
        process = subprocess.Popen(['python3', script_name])
        time.sleep(3)
        
        # 检查服务是否启动成功
        try:
            if port == 5000:
                url = f"http://127.0.0.1:{port}/health"
            else:
                url = f"http://127.0.0.1:{port}"
                
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {service_name} 启动成功 (端口: {port})")
                return True
            else:
                print(f"❌ {service_name} 响应异常: {response.status_code}")
                return False
        except:
            print(f"❌ {service_name} 连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 启动 {service_name} 失败: {e}")
        return False

def main():
    print("🚀 MQTT位置监控系统 - 服务启动")
    print("=" * 40)
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    services = [
        ('receiver_improved.py', 5000, 'MQTT接收服务'),
        ('baidu_map_fixed.py', 8081, '百度地图服务')
    ]
    
    success_count = 0
    for script, port, name in services:
        if start_service(script, port, name):
            success_count += 1
        print()
    
    print("=" * 40)
    if success_count == len(services):
        print("🎉 所有服务启动成功！")
        print()
        print("📱 访问地址:")
        print("  🗺️ 地图监控: http://**************:8081")
        print("  📊 服务状态: http://**************:5000/health")
        print()
        print("🧪 测试MQTT:")
        print('  mosquitto_pub -h 127.0.0.1 -t "location/test" -m \'{"latitude":39.9042,"longitude":116.4074,"name":"测试"}\'')
    else:
        print(f"⚠️ {success_count}/{len(services)} 个服务启动成功")

if __name__ == "__main__":
    main()
