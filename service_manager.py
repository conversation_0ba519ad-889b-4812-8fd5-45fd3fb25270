#!/usr/bin/env python3
"""
MQTT地理位置监控服务管理器
用于启动、停止和管理各个服务组件
"""

import subprocess
import sys
import time
import signal
import os
from datetime import datetime

class ServiceManager:
    def __init__(self):
        self.services = {
            'receiver': {
                'file': 'receiver_improved.py',  # 修正文件名
                'port': 5000,
                'description': 'MQTT数据接收服务',
                'process': None
            }
        }
        
    def start_service(self, service_name):
        """启动指定服务"""
        if service_name not in self.services:
            print(f"❌ 未知服务: {service_name}")
            return False
            
        service = self.services[service_name]
        
        if service['process'] and service['process'].poll() is None:
            print(f"⚠️ 服务 {service_name} 已经在运行中")
            return True
            
        try:
            print(f"🚀 启动服务: {service['description']}")
            print(f"📁 文件: {service['file']}")
            print(f"🔌 端口: {service['port']}")
            
            service['process'] = subprocess.Popen([
                'python3', service['file']
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            time.sleep(2)  # 等待服务启动
            
            if service['process'].poll() is None:
                print(f"✅ 服务 {service_name} 启动成功 (PID: {service['process'].pid})")
                return True
            else:
                stdout, stderr = service['process'].communicate()
                print(f"❌ 服务 {service_name} 启动失败")
                print(f"错误信息: {stderr.decode()}")
                return False
                
        except Exception as e:
            print(f"❌ 启动服务 {service_name} 时出错: {e}")
            return False
    
    def stop_service(self, service_name):
        """停止指定服务"""
        if service_name not in self.services:
            print(f"❌ 未知服务: {service_name}")
            return False
            
        service = self.services[service_name]
        
        if not service['process'] or service['process'].poll() is not None:
            print(f"⚠️ 服务 {service_name} 未在运行")
            return True
            
        try:
            print(f"🛑 停止服务: {service['description']}")
            service['process'].terminate()
            
            # 等待进程结束
            try:
                service['process'].wait(timeout=5)
                print(f"✅ 服务 {service_name} 已停止")
            except subprocess.TimeoutExpired:
                print(f"⚠️ 强制终止服务 {service_name}")
                service['process'].kill()
                service['process'].wait()
                
            service['process'] = None
            return True
            
        except Exception as e:
            print(f"❌ 停止服务 {service_name} 时出错: {e}")
            return False
    
    def status(self):
        """显示所有服务状态"""
        print(f"📊 服务状态 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        for name, service in self.services.items():
            status = "🟢 运行中" if (service['process'] and service['process'].poll() is None) else "🔴 已停止"
            pid = f"(PID: {service['process'].pid})" if (service['process'] and service['process'].poll() is None) else ""
            
            print(f"{name:10} | {status:8} | 端口:{service['port']:5} | {service['description']} {pid}")
    
    def start_all(self):
        """启动所有服务"""
        print("🚀 启动所有服务...")
        success_count = 0
        
        for service_name in self.services.keys():
            if self.start_service(service_name):
                success_count += 1
            print()
            
        print(f"✅ 成功启动 {success_count}/{len(self.services)} 个服务")
    
    def stop_all(self):
        """停止所有服务"""
        print("🛑 停止所有服务...")
        
        for service_name in self.services.keys():
            self.stop_service(service_name)
            
        print("✅ 所有服务已停止")

def main():
    manager = ServiceManager()
    
    if len(sys.argv) < 2:
        print("📋 MQTT地理位置监控服务管理器")
        print("用法:")
        print("  python3 service_manager.py start <service_name>  # 启动指定服务")
        print("  python3 service_manager.py stop <service_name>   # 停止指定服务") 
        print("  python3 service_manager.py start-all            # 启动所有服务")
        print("  python3 service_manager.py stop-all             # 停止所有服务")
        print("  python3 service_manager.py status               # 查看服务状态")
        print()
        print("可用服务:")
        for name, service in manager.services.items():
            print(f"  {name:10} - {service['description']} (端口:{service['port']})")
        return
    
    command = sys.argv[1]
    
    if command == "start" and len(sys.argv) > 2:
        manager.start_service(sys.argv[2])
    elif command == "stop" and len(sys.argv) > 2:
        manager.stop_service(sys.argv[2])
    elif command == "start-all":
        manager.start_all()
    elif command == "stop-all":
        manager.stop_all()
    elif command == "status":
        manager.status()
    else:
        print("❌ 无效命令")

if __name__ == "__main__":
    main()
