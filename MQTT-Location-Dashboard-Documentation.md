# MQTT 地理位置监控系统

## 📋 项目概述

基于 **EMQX + MySQL + Flask + 百度地图** 的实时位置监控系统，支持通过MQTT协议接收设备位置数据，并在Web界面上实时显示设备位置和传感器数据。

## 🏗️ 系统架构

```
MQTT客户端 → EMQX Broker → HTTP Bridge → 接收服务 → MySQL → Web服务 → 地图界面
```

### 核心组件

| 组件 | 端口 | 功能描述 |
|------|------|----------|
| **EMQX MQTT Broker** | 1883, 18083 | MQTT消息代理，HTTP Bridge转发 |
| **接收服务** | 5000 | 接收EMQX转发的HTTP请求，存储到MySQL |
| **地图服务** | 8081 | 百度地图Web界面，实时显示位置 |
| **稳定版面板** | 8082 | 监控面板，显示消息列表和统计 |
| **MySQL数据库** | 3306 | 存储MQTT消息和位置数据 |

## 📁 项目目录结构

```
/opt/mqtt-to-mysql/
├── 🔧 核心服务文件
│   ├── receiver_fixed.py          # MQTT数据接收服务 (端口: 5000)
│   ├── baidu_map_fixed.py         # 百度地图显示服务 (端口: 8081)
│   └── stable_dashboard.py        # 稳定版监控面板 (端口: 8082)
│
├── 🛠️ 管理工具
│   ├── service_manager.py         # 服务启停管理工具
│   ├── deploy.py                  # 一键部署脚本
│   ├── quick_fix.py              # 快速问题修复工具
│   └── emqx_bridge_config.py     # EMQX桥接配置工具
│
├── 🧪 测试脚本
│   ├── test_data_flow.py         # 数据流测试
│   ├── check_server_status.py    # 服务器状态检查
│   ├── check_mysql_schema.py     # 数据库结构检查
│   └── check_emqx_bridge.py      # EMQX桥接检查
│
├── 📄 配置和文档
│   ├── README.md                 # 项目说明文档
│   └── cleanup_project.py        # 项目清理工具
│
└── 🗂️ 备份文件
    └── backup/                   # 旧版本文件备份
```

## 🔧 技术栈

### 后端技术
- **Python 3.x** - 主要开发语言
- **Flask** - Web框架
- **MySQL** - 数据存储
- **mysql-connector-python** - 数据库连接器
- **EMQX** - MQTT消息代理

### 前端技术
- **HTML5/CSS3/JavaScript** - 基础前端技术
- **百度地图API** - 地图显示
- **Leaflet.js** - 备用地图库
- **Bootstrap** - UI框架

### 部署环境
- **Linux服务器** (推荐Ubuntu/CentOS)
- **Docker** (可选，用于EMQX部署)
- **Nginx** (可选，用于反向代理)

## 🗄️ 数据库设计

### 数据库配置
```python
mysql_config = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'emqx',
    'password': 'EmqxPass!123',
    'database': 'emqx_data'
}
```

### 表结构
```sql
CREATE TABLE mqtt_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id VARCHAR(255),           -- MQTT客户端ID
    topic VARCHAR(255),               -- MQTT主题
    payload TEXT,                     -- 消息内容(JSON格式)
    qos INT DEFAULT 0,                -- 服务质量等级
    arrived TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 到达时间
    INDEX idx_topic (topic),
    INDEX idx_arrived (arrived)
);
```

### 数据格式示例
```json
{
    "lat": 39.920,        // 纬度
    "lng": 116.410,       // 经度
    "gas": 2500,          // 气体传感器数值
    "temp": 28.0,         // 温度
    "humidity": 70,       // 湿度
    "pressure": 1012.8    // 气压
}
```

## 🚀 部署指南

### 1. 环境准备

#### 系统要求
- Linux服务器 (Ubuntu 18.04+ / CentOS 7+)
- Python 3.6+
- MySQL 5.7+ / 8.0+
- 至少2GB内存，10GB磁盘空间

#### 安装依赖
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python和pip
sudo apt install python3 python3-pip -y

# 安装MySQL
sudo apt install mysql-server -y

# 安装Python依赖包
pip3 install flask mysql-connector-python paho-mqtt requests
```

### 2. 数据库配置

```bash
# 登录MySQL
sudo mysql -u root -p

# 创建数据库和用户
CREATE DATABASE emqx_data;
CREATE USER 'emqx'@'localhost' IDENTIFIED BY 'EmqxPass!123';
GRANT ALL PRIVILEGES ON emqx_data.* TO 'emqx'@'localhost';
FLUSH PRIVILEGES;
```

### 3. EMQX安装配置

```bash
# 使用Docker安装EMQX
docker run -d --name emqx \
  -p 1883:1883 \
  -p 8083:8083 \
  -p 8084:8084 \
  -p 8883:8883 \
  -p 18083:18083 \
  emqx/emqx:latest

# 访问管理界面: http://your-server-ip:18083
# 默认用户名: admin, 密码: public
```

### 4. 项目部署

```bash
# 创建项目目录
sudo mkdir -p /opt/mqtt-to-mysql
cd /opt/mqtt-to-mysql

# 下载项目文件 (假设从Git仓库)
# git clone your-repository .

# 使用一键部署脚本
python3 deploy.py

# 或手动启动服务
python3 service_manager.py start-all
```

## 🎮 使用指南

### 1. 启动服务

```bash
cd /opt/mqtt-to-mysql

# 查看服务状态
python3 service_manager.py status

# 启动所有服务
python3 service_manager.py start-all

# 启动单个服务
python3 service_manager.py start receiver
python3 service_manager.py start map
python3 service_manager.py start stable
```

### 2. 发送测试数据

```bash
# 使用mosquitto客户端发送测试消息
mosquitto_pub -h localhost -t "location/device_001" \
  -m '{"lat":39.920,"lng":116.410,"gas":2500,"temp":28.0,"humidity":70,"pressure":1012.8}'
```

### 3. 访问Web界面

- **地图监控**: http://your-server-ip:8081
- **监控面板**: http://your-server-ip:8082
- **EMQX管理**: http://your-server-ip:18083
- **服务健康检查**: http://your-server-ip:5000/health

## 🔧 EMQX配置

### HTTP Bridge配置

1. **连接器配置**
   - 名称: `mysql-http-bridge`
   - URL: `http://127.0.0.1:5000/mqtt/data`
   - 方法: POST
   - 请求头: `Content-Type: application/json`

2. **规则配置**
   - 名称: `mqtt-mysql`
   - SQL: `SELECT clientid as client_id, topic, payload, qos, retain, timestamp as arrived FROM "location/+"`
   - 动作: 转发到HTTP Bridge

### 请求体模板
```json
{
  "client_id": "${client_id}",
  "topic": "${topic}",
  "payload": "${payload}",
  "qos": ${qos},
  "retain": ${retain},
  "timestamp": "${timestamp}"
}
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep -E "(5000|8081|8082|1883|18083)"
   
   # 杀死占用进程
   sudo pkill -f "python.*receiver"
   sudo pkill -f "python.*baidu_map"
   ```

2. **数据库连接失败**
   ```bash
   # 检查MySQL服务
   sudo systemctl status mysql
   
   # 测试数据库连接
   python3 check_mysql_schema.py
   ```

3. **EMQX连接问题**
   ```bash
   # 检查EMQX状态
   docker ps | grep emqx
   
   # 查看EMQX日志
   docker logs emqx
   ```

4. **数据不显示**
   - 检查EMQX HTTP Bridge配置
   - 验证MySQL表结构
   - 确认MQTT消息格式正确
   - 查看服务日志

### 日志查看

```bash
# 查看服务日志
tail -f /var/log/mqtt-receiver.log
tail -f /var/log/mqtt-map.log

# 查看系统日志
journalctl -u mqtt-receiver -f
```

## 🧪 测试验证

### 数据流测试
```bash
# 运行完整数据流测试
python3 test_data_flow.py

# 检查服务器状态
python3 check_server_status.py

# 验证EMQX桥接
python3 check_emqx_bridge.py
```

### API测试
```bash
# 健康检查
curl http://localhost:5000/health

# 获取地图数据
curl http://localhost:8081/api/map-data

# 获取消息列表
curl http://localhost:8082/api/data
```

## 📊 监控和维护

### 性能监控
- CPU和内存使用率
- 数据库连接数
- MQTT消息吞吐量
- Web服务响应时间

### 定期维护
- 清理过期日志文件
- 备份数据库数据
- 更新系统和依赖包
- 监控磁盘空间使用

### 扩展建议
- 添加Redis缓存提升性能
- 使用Nginx负载均衡
- 实现数据备份和恢复
- 添加用户认证和权限管理

## 📞 技术支持

如有问题，请检查：
1. 服务状态和日志
2. 网络连接和防火墙设置
3. 数据库权限和表结构
4. EMQX配置和规则

## 🔄 数据流程详解

### 完整数据流
1. **MQTT客户端** 发送位置数据到主题 `location/device_id`
2. **EMQX Broker** 接收MQTT消息
3. **HTTP Bridge规则** 匹配主题并转发到接收服务
4. **接收服务** (receiver_fixed.py) 解析数据并存储到MySQL
5. **Web服务** 从MySQL读取数据
6. **前端界面** 实时显示位置和传感器数据

### 消息格式规范

#### MQTT主题格式
```
location/{device_id}
```

#### Payload格式 (JSON)
```json
{
  "lat": 39.920,          // 必需：纬度 (-90 到 90)
  "lng": 116.410,         // 必需：经度 (-180 到 180)
  "gas": 2500,            // 可选：气体传感器数值
  "temp": 28.0,           // 可选：温度 (摄氏度)
  "humidity": 70,         // 可选：湿度 (0-100%)
  "pressure": 1012.8,     // 可选：气压 (hPa)
  "battery": 85,          // 可选：电池电量 (0-100%)
  "signal": -65,          // 可选：信号强度 (dBm)
  "timestamp": "2025-01-13T10:30:00Z"  // 可选：时间戳
}
```

## 🛡️ 安全配置

### 防火墙设置
```bash
# 开放必要端口
sudo ufw allow 1883/tcp    # MQTT
sudo ufw allow 18083/tcp   # EMQX管理界面
sudo ufw allow 5000/tcp    # 接收服务
sudo ufw allow 8081/tcp    # 地图服务
sudo ufw allow 8082/tcp    # 监控面板
sudo ufw enable
```

### MySQL安全配置
```sql
-- 限制用户权限
REVOKE ALL PRIVILEGES ON *.* FROM 'emqx'@'localhost';
GRANT SELECT, INSERT, UPDATE ON emqx_data.* TO 'emqx'@'localhost';

-- 设置强密码策略
SET GLOBAL validate_password.policy = STRONG;
```

### EMQX认证配置
```bash
# 在EMQX管理界面配置
# 认证 -> 密码认证 -> 添加用户
# 授权 -> ACL -> 配置主题权限
```

## 📈 性能优化

### 数据库优化
```sql
-- 添加索引优化查询
CREATE INDEX idx_client_topic ON mqtt_messages(client_id, topic);
CREATE INDEX idx_arrived_desc ON mqtt_messages(arrived DESC);

-- 定期清理旧数据
DELETE FROM mqtt_messages WHERE arrived < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 应用优化
- 使用连接池管理数据库连接
- 实现数据缓存减少数据库查询
- 压缩HTTP响应减少带宽使用
- 使用CDN加速静态资源

## 🔧 高级配置

### 集群部署
```yaml
# docker-compose.yml 示例
version: '3.8'
services:
  emqx1:
    image: emqx/emqx:latest
    environment:
      - EMQX_NODE_NAME=<EMAIL>
      - EMQX_CLUSTER__DISCOVERY_STRATEGY=static
      - EMQX_CLUSTER__STATIC__SEEDS=<EMAIL>,<EMAIL>
    ports:
      - "1883:1883"
      - "18083:18083"

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpass
      - MYSQL_DATABASE=emqx_data
      - MYSQL_USER=emqx
      - MYSQL_PASSWORD=EmqxPass!123
    volumes:
      - mysql_data:/var/lib/mysql
```

### 负载均衡配置
```nginx
# nginx.conf
upstream mqtt_receivers {
    server 127.0.0.1:5000;
    server 127.0.0.1:5001;
    server 127.0.0.1:5002;
}

server {
    listen 80;
    location /mqtt/ {
        proxy_pass http://mqtt_receivers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📋 运维脚本

### 自动备份脚本
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/backups"

# 备份数据库
mysqldump -u emqx -pEmqxPass!123 emqx_data > $BACKUP_DIR/emqx_data_$DATE.sql

# 备份配置文件
tar -czf $BACKUP_DIR/config_$DATE.tar.gz /opt/mqtt-to-mysql/*.py

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### 健康检查脚本
```bash
#!/bin/bash
# health_check.sh
SERVICES=("5000" "8081" "8082" "1883" "18083")

for port in "${SERVICES[@]}"; do
    if nc -z localhost $port; then
        echo "✅ Port $port is open"
    else
        echo "❌ Port $port is closed"
        # 发送告警通知
        # curl -X POST "https://api.telegram.org/bot<token>/sendMessage" \
        #      -d "chat_id=<chat_id>&text=Service on port $port is down"
    fi
done
```

## 🚨 告警配置

### 系统监控告警
- CPU使用率超过80%
- 内存使用率超过85%
- 磁盘空间使用率超过90%
- 数据库连接数超过阈值

### 业务监控告警
- MQTT消息接收中断
- 数据库写入失败
- Web服务响应超时
- 设备离线超过设定时间

## 📚 API文档

### 接收服务API (端口5000)

#### POST /mqtt/data
接收EMQX转发的MQTT数据
```json
// 请求体
{
  "client_id": "device_001",
  "topic": "location/device_001",
  "payload": "{\"lat\":39.920,\"lng\":116.410}",
  "qos": 0,
  "retain": false,
  "timestamp": "2025-01-13T10:30:00Z"
}

// 响应
{
  "status": "success"
}
```

#### GET /health
健康检查接口
```json
{
  "status": "healthy",
  "timestamp": "2025-01-13T10:30:00Z"
}
```

### 地图服务API (端口8081)

#### GET /api/map-data
获取地图显示数据
```json
{
  "status": "success",
  "locations": [
    {
      "device_id": "device_001",
      "lat": 39.920,
      "lng": 116.410,
      "last_update": "2025-01-13 10:30:00",
      "sensors": {
        "temp": 28.0,
        "humidity": 70,
        "gas": 2500
      }
    }
  ],
  "total_devices": 1,
  "online_devices": 1
}
```

### 监控面板API (端口8082)

#### GET /api/data
获取消息列表
```json
{
  "status": "success",
  "messages": [
    {
      "id": 1,
      "client_id": "device_001",
      "topic": "location/device_001",
      "payload": "{\"lat\":39.920,\"lng\":116.410}",
      "arrived": "2025-01-13 10:30:00"
    }
  ],
  "timestamp": "2025-01-13T10:30:00Z"
}
```

---

**项目版本**: v1.0
**最后更新**: 2025-01-13
**部署位置**: `/opt/mqtt-to-mysql`
**文档版本**: 详细版 v1.1
