#!/usr/bin/env python3
import json
import mysql.connector
from flask import Flask, request, jsonify
from datetime import datetime
import logging

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

mysql_config = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'emqx',
    'password': 'EmqxPass!123',
    'database': 'emqx_data'
}

@app.route('/mqtt/data', methods=['POST'])
def receive_mqtt_data():
    try:
        raw_data = request.get_data(as_text=True)
        app.logger.info(f"Raw request data: {raw_data}")
        
        data = request.get_json()
        app.logger.info(f"Parsed JSON data: {data}")
        
        conn = mysql.connector.connect(**mysql_config)
        cursor = conn.cursor()
        
        sql = """INSERT INTO mqtt_messages 
                 (client_id, topic, payload, qos, retain, arrived) 
                 VALUES (%s, %s, %s, %s, %s, %s)"""
        
        values = (
            data.get('client_id', 'unknown'),
            data.get('topic', 'unknown'),
            str(data.get('payload', '')),
            data.get('qos', 0),
            data.get('retain', 0),
            datetime.now()
        )
        
        cursor.execute(sql, values)
        conn.commit()
        cursor.close()
        conn.close()
        
        app.logger.info("Data inserted successfully")
        return jsonify({"status": "success"}), 200
        
    except Exception as e:
        app.logger.error(f"Error: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy"}), 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
