# 天然气管道巡检监控系统 - 完整部署指南

## 📋 系统概述

这是一个基于 **EMQX + MySQL + Flask + 百度地图** 的完整天然气管道巡检监控系统，支持实时数据采集、地图显示、状态管理和数据分析。

### 🎯 核心功能
- 🌍 **实时地图监控** - 百度地图显示设备位置和气体浓度
- 📊 **多维数据分析** - 气体浓度、温度、湿度、压力等传感器数据
- 🔔 **实时消息推送** - 新数据到达时立即更新界面
- 🏷️ **智能状态管理** - 消息标记、已读、删除等状态持久化
- 📱 **响应式界面** - 支持PC和移动端访问
- 🔍 **多维度筛选** - 时间范围、气体浓度、设备类型等筛选功能

## 🏗️ 系统架构

```
┌─────────────┐    ┌──────────┐    ┌─────────────────┐    ┌─────────┐    ┌─────────────────┐
│  MQTT设备   │───▶│   EMQX   │───▶│ receiver_improved│───▶│  MySQL  │───▶│gas_pipeline_    │
│  (传感器)   │    │  Broker  │    │     .py         │    │Database │    │inspection.py    │
└─────────────┘    └──────────┘    └─────────────────┘    └─────────┘    └─────────────────┘
      ↓                  ↓                   ↓                 ↓                   ↓
   传感器数据          消息路由           HTTP接收/解析        数据存储           Web界面显示
```

### 🔧 服务组件详解

#### 1. **EMQX MQTT Broker**
- **端口**: 1883 (MQTT), 18083 (管理界面)
- **功能**: MQTT消息代理和路由
- **配置**: HTTP Bridge转发消息到接收服务
- **管理界面**: http://**************:18083 (admin/public)

#### 2. **MQTT接收服务** - `receiver_improved.py`
- **端口**: 5000
- **功能**: 
  - 接收EMQX转发的HTTP请求
  - 解析传感器数据（气体、温度、湿度、压力等）
  - 数据验证和格式化
  - 存储到MySQL数据库
- **健康检查**: http://**************:5000/health
- **API接口**: http://**************:5000/api/stats

#### 3. **Web监控界面** - `gas_pipeline_inspection.py`
- **端口**: 8082
- **功能**:
  - 实时数据展示和百度地图显示
  - 消息状态管理（标记、已读、删除）
  - 数据筛选和统计分析
  - 实时刷新和消息推送
  - 响应式界面设计
- **访问地址**: http://**************:8082 ⭐ **主要访问地址**

#### 4. **MySQL数据库**
- **端口**: 3306
- **数据库**: `emqx_data`
- **主表**: `mqtt_messages`
- **认证**: 用户 `emqx` / 密码 `EmqxPass!123`

## 🌐 服务器环境信息

### 📍 部署环境
- **服务器**: 阿里云ECS
- **操作系统**: Linux (Ubuntu/CentOS)
- **部署路径**: `/opt/mqtt-to-mysql/`
- **宿主机IP**: `**************`
- **Python版本**: Python 3.x

### 🔗 网络配置
- **EMQX连接器URL**: `http://**************:5000/mqtt/data` ⚠️ **必须使用宿主机IP**
- **EMQX管理界面**: `http://**************:18083`
- **监控系统访问**: `http://**************:8082`
- **接收服务健康检查**: `http://**************:5000/health`

## 🚀 完整部署流程

### 第一步：环境准备

#### 1.1 安装依赖包
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装Python和pip
sudo apt install python3 python3-pip -y

# 安装MySQL客户端
sudo apt install mysql-client -y

# 安装Python依赖
pip3 install flask mysql-connector-python pymysql requests
```

#### 1.2 创建项目目录
```bash
# 创建项目目录
sudo mkdir -p /opt/mqtt-to-mysql
cd /opt/mqtt-to-mysql

# 设置权限
sudo chown -R $USER:$USER /opt/mqtt-to-mysql
```

### 第二步：数据库配置

#### 2.1 MySQL数据库设置
```sql
-- 创建数据库
CREATE DATABASE emqx_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'emqx'@'%' IDENTIFIED BY 'EmqxPass!123';
GRANT ALL PRIVILEGES ON emqx_data.* TO 'emqx'@'%';
FLUSH PRIVILEGES;

-- 创建消息表
USE emqx_data;
CREATE TABLE mqtt_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id VARCHAR(255),
    topic VARCHAR(255),
    payload TEXT,
    arrived TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    qos INT DEFAULT 0,
    retain BOOLEAN DEFAULT FALSE,
    INDEX idx_arrived (arrived),
    INDEX idx_topic (topic),
    INDEX idx_client_id (client_id)
);
```

#### 2.2 验证数据库连接
```bash
mysql -u emqx -pEmqxPass!123 emqx_data -e "SELECT COUNT(*) FROM mqtt_messages;"
```

### 第三步：EMQX配置

#### 3.1 EMQX安装和启动
```bash
# 使用Docker安装EMQX (推荐)
docker run -d --name emqx \
  -p 1883:1883 \
  -p 8083:8083 \
  -p 8084:8084 \
  -p 8883:8883 \
  -p 18083:18083 \
  emqx/emqx:latest
```

#### 3.2 配置HTTP Bridge连接器
1. 访问EMQX管理界面: http://**************:18083
2. 登录: 用户名 `admin`, 密码 `public`
3. 进入 **集成** → **连接器** → **创建连接器**
4. 选择 **HTTP Server**
5. 配置连接器:
   - **名称**: `mysql-http-bridge`
   - **URL**: `http://**************:5000/mqtt/data` ⚠️ **必须使用宿主机IP**
   - **方法**: `POST`
   - **请求头**: `Content-Type: application/json`

#### 3.3 创建规则和动作
1. 进入 **集成** → **规则**
2. 创建规则:
   - **名称**: `mqtt-mysql`
   - **SQL**: `SELECT clientid as client_id, topic, payload, qos, retain, timestamp as arrived FROM "location/+"`
3. 添加动作:
   - 选择之前创建的 `mysql-http-bridge` 连接器
   - **请求体模板**:
   ```json
   {
     "client_id": "${client_id}",
     "topic": "${topic}",
     "payload": "${payload}",
     "qos": ${qos},
     "retain": ${retain},
     "timestamp": "${timestamp}"
   }
   ```

### 第四步：部署核心服务

#### 4.1 上传核心文件
将以下文件上传到 `/opt/mqtt-to-mysql/`:
- `gas_pipeline_inspection.py` - Web监控界面
- `receiver_improved.py` - MQTT接收服务
- `manage_services.sh` - 服务管理脚本
- `clear_database.py` - 数据库清空工具
- `README.md` - 项目文档

#### 4.2 设置文件权限
```bash
cd /opt/mqtt-to-mysql
chmod +x manage_services.sh
chmod +x clear_database.py
```

### 第五步：启动和验证服务

#### 5.1 启动所有服务
```bash
cd /opt/mqtt-to-mysql
./manage_services.sh start-all
```

#### 5.2 检查服务状态
```bash
./manage_services.sh status
```

预期输出:
```
🟢 gas_pipeline_inspection.py 正在运行 (PID: XXXXX)
🟢 receiver_improved.py 正在运行 (PID: XXXXX)
```

#### 5.3 验证服务可访问性
```bash
# 检查MQTT接收服务
curl http://**************:5000/health

# 检查Web界面 (浏览器访问)
# http://**************:8082
```

## 📡 MQTT数据格式规范

### 发送主题
```
location/{device_id}
```

### 数据格式
```json
{
    "lat": 39.9042,           // 纬度 (必需)
    "lng": 116.4074,          // 经度 (必需)
    "gas": 2500,              // 气体浓度 (ppm)
    "temp": 28.0,             // 温度 (°C)
    "humidity": 70,           // 湿度 (%)
    "pressure": 1012.8,       // 压力 (MPa)
    "device_id": "device_001", // 设备ID
    "name": "巡检点A",         // 设备名称
    "timestamp": "2025-08-13T20:30:00"
}
```

### 支持的坐标字段
- 纬度: `lat`, `latitude`
- 经度: `lng`, `lon`, `longitude`

## 🧪 系统测试

### 测试数据发送
```bash
# 使用mosquitto客户端发送测试数据
mosquitto_pub -h ************** -t "location/test001" -m '{
  "lat": 39.920,
  "lng": 116.410,
  "gas": 2500,
  "temp": 28.0,
  "humidity": 70,
  "pressure": 1012.8,
  "device_id": "test001",
  "name": "测试设备"
}'
```

### 验证数据流
1. **EMQX规则统计** - 检查消息是否被处理
2. **接收服务日志** - 查看HTTP请求接收情况
3. **数据库记录** - 确认数据已存储
4. **Web界面显示** - 验证地图上显示设备位置

```bash
# 查看数据库记录
mysql -u emqx -pEmqxPass!123 emqx_data -e "SELECT * FROM mqtt_messages ORDER BY id DESC LIMIT 5;"
```

## 🔧 日常运维管理

### 服务管理命令

#### 启动服务
```bash
cd /opt/mqtt-to-mysql

# 启动所有服务
./manage_services.sh start-all

# 分别启动服务
./manage_services.sh start-web    # 启动Web界面
./manage_services.sh start-mqtt   # 启动MQTT接收器
```

#### 停止服务
```bash
# 停止所有服务
./manage_services.sh stop-all

# 分别停止服务
./manage_services.sh stop-web     # 停止Web界面
./manage_services.sh stop-mqtt    # 停止MQTT接收器
```

#### 重启服务
```bash
# 重启所有服务 (推荐用于解决端口占用等问题)
./manage_services.sh restart-all

# 分别重启服务
./manage_services.sh restart-web
./manage_services.sh restart-mqtt
```

#### 查看状态和日志
```bash
# 查看服务状态
./manage_services.sh status

# 查看实时日志
./manage_services.sh logs-web     # Web服务日志
./manage_services.sh logs-mqtt    # MQTT服务日志

# 查看历史日志
tail -100 gas_pipeline.log        # Web服务历史日志
tail -100 receiver.log             # MQTT服务历史日志
```

### 数据库管理

#### 数据库维护
```bash
# 查看数据统计
mysql -u emqx -pEmqxPass!123 emqx_data -e "
SELECT
    COUNT(*) as total_messages,
    MIN(arrived) as first_message,
    MAX(arrived) as last_message,
    COUNT(DISTINCT client_id) as unique_devices
FROM mqtt_messages;"

# 清空数据库 (重新开始)
python3 clear_database.py

# 删除7天前的旧数据 (可选)
mysql -u emqx -pEmqxPass!123 emqx_data -e "
DELETE FROM mqtt_messages
WHERE arrived < DATE_SUB(NOW(), INTERVAL 7 DAY);"
```

#### 数据备份
```bash
# 备份数据库
mysqldump -u emqx -pEmqxPass!123 emqx_data > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
mysql -u emqx -pEmqxPass!123 emqx_data < backup_20250813_220000.sql
```

### 系统监控

#### 资源监控
```bash
# 查看服务进程和资源使用
ps aux | grep -E "(gas_pipeline|receiver)" | grep -v grep

# 查看端口占用
netstat -tlnp | grep -E "(5000|8082|1883|3306)"

# 查看磁盘使用
df -h /opt/mqtt-to-mysql/

# 查看内存使用
free -h
```

#### 日志监控
```bash
# 监控错误日志
tail -f gas_pipeline.log | grep -i error
tail -f receiver.log | grep -i error

# 监控数据接收情况
tail -f receiver.log | grep "收到MQTT数据"

# 查看最近的访问日志
tail -50 gas_pipeline.log | grep "GET\|POST"
```

## 🔍 故障排除指南

### 常见问题及解决方案

#### 1. 服务启动失败

**问题**: 端口占用导致服务无法启动
```bash
# 症状
Address already in use
Port 5000 is in use by another program
```

**解决方案**:
```bash
# 查找占用端口的进程
lsof -i :5000
lsof -i :8082

# 强制释放端口
sudo fuser -k 5000/tcp
sudo fuser -k 8082/tcp

# 清理残留进程
pkill -9 -f gas_pipeline_inspection
pkill -9 -f receiver_improved

# 重新启动
./manage_services.sh restart-all
```

#### 2. 数据不显示或EMQX数据丢弃

**问题**: Web界面显示"正在加载地图数据..."或数据不更新

**诊断步骤**:
```bash
# 1. 检查EMQX连接器状态
curl http://**************:5000/health

# 2. 检查EMQX管理界面中的连接器配置
# URL必须是: http://**************:5000/mqtt/data (使用宿主机IP)

# 3. 查看EMQX规则统计
# 进入EMQX管理界面 → 集成 → 规则 → 查看统计

# 4. 检查数据库连接
mysql -u emqx -pEmqxPass!123 emqx_data -e "SELECT COUNT(*) FROM mqtt_messages;"
```

**解决方案**:
```bash
# 修复EMQX连接器URL (最常见问题)
# 1. 访问 http://**************:18083
# 2. 进入 集成 → 连接器 → 编辑 mysql-http-bridge
# 3. 修改URL为: http://**************:5000/mqtt/data
# 4. 点击"测试连接"确保成功
# 5. 点击"更新"保存配置
```

#### 3. 实时刷新不工作

**问题**: 页面不能实时更新新数据

**诊断方法**:
```bash
# 1. 打开浏览器开发者工具 (F12)
# 2. 查看Console标签页的JavaScript错误
# 3. 点击页面上的"调试"按钮查看消息ID状态
# 4. 观察Network标签页的API请求
```

**解决方案**:
- 刷新浏览器页面
- 清除浏览器缓存
- 检查浏览器是否阻止了JavaScript执行
- 确认lastMessageId是否正确初始化

#### 4. 数据库连接问题

**问题**: 数据库连接失败或权限错误

**解决方案**:
```bash
# 测试数据库连接
mysql -u emqx -pEmqxPass!123 -h 127.0.0.1 emqx_data

# 重新设置用户权限
mysql -u root -p -e "
GRANT ALL PRIVILEGES ON emqx_data.* TO 'emqx'@'%';
GRANT ALL PRIVILEGES ON emqx_data.* TO 'emqx'@'localhost';
FLUSH PRIVILEGES;"

# 检查表结构
mysql -u emqx -pEmqxPass!123 emqx_data -e "DESCRIBE mqtt_messages;"
```

### 紧急恢复流程

#### 系统完全重启
```bash
# 1. 停止所有服务
./manage_services.sh stop-all
sleep 5

# 2. 清理所有相关进程
pkill -9 -f gas_pipeline
pkill -9 -f receiver
pkill -9 -f python3

# 3. 清理端口占用
sudo fuser -k 5000/tcp
sudo fuser -k 8082/tcp
sleep 3

# 4. 重新启动所有服务
./manage_services.sh start-all

# 5. 验证服务状态
./manage_services.sh status
curl http://**************:5000/health
```

#### 数据库紧急恢复
```bash
# 如果数据库出现问题，重建表结构
mysql -u emqx -pEmqxPass!123 emqx_data -e "
DROP TABLE IF EXISTS mqtt_messages;
CREATE TABLE mqtt_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id VARCHAR(255),
    topic VARCHAR(255),
    payload TEXT,
    arrived TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    qos INT DEFAULT 0,
    retain BOOLEAN DEFAULT FALSE,
    INDEX idx_arrived (arrived),
    INDEX idx_topic (topic),
    INDEX idx_client_id (client_id)
);"
```

## 📊 性能优化建议

### 数据库优化
```sql
-- 定期清理旧数据 (保留最近30天)
DELETE FROM mqtt_messages WHERE arrived < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 优化表结构
OPTIMIZE TABLE mqtt_messages;

-- 分析表性能
ANALYZE TABLE mqtt_messages;
```

### 系统优化
```bash
# 设置系统服务自启动 (可选)
sudo systemctl enable mysql
sudo systemctl enable docker  # 如果使用Docker运行EMQX

# 配置日志轮转 (防止日志文件过大)
sudo logrotate -f /etc/logrotate.conf
```

## 🎯 系统监控指标

### 关键指标
- **消息接收率**: 每分钟接收的MQTT消息数量
- **数据库增长**: 数据库记录数和存储空间增长
- **服务响应时间**: Web界面和API的响应时间
- **错误率**: 服务错误和异常的发生频率
- **资源使用**: CPU、内存、磁盘使用率

### 监控命令
```bash
# 实时监控消息接收
tail -f receiver.log | grep "收到MQTT数据" | while read line; do echo "$(date): $line"; done

# 监控数据库增长
watch -n 60 'mysql -u emqx -pEmqxPass!123 emqx_data -e "SELECT COUNT(*) as total FROM mqtt_messages;"'

# 监控系统资源
htop  # 或者 top
```

---

**文档版本**: 2.0.0
**最后更新**: 2025-08-13
**适用环境**: 阿里云ECS + EMQX + MySQL + Flask
**维护者**: BRIDGES
