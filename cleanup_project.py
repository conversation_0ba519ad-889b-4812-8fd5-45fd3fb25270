#!/usr/bin/env python3
"""
项目文件清理脚本
整理项目结构，移动旧文件到备份目录
"""

import os
import shutil
from datetime import datetime

class ProjectCleaner:
    def __init__(self):
        self.backup_dir = "backup_files"
        self.current_files = [
            "baidu_map_fixed.py",      # 主要地图服务
            "receiver_fixed.py",       # MQTT接收服务
            "service_manager.py",      # 服务管理工具
            "test_data_flow.py",       # 数据流测试
            "check_server_status.py",  # 状态检查
            "emqx_bridge_config.py",   # EMQX配置
            "cleanup_project.py",      # 本脚本
            "README.md",               # 文档
            "templates"                # 模板目录
        ]
        
        self.files_to_backup = [
            "map_dashboard_backup.py",
            "receiver.py",
            "receiver_debug.py", 
            "receiver_simple.py",
            "mqtt_final.py",
            "mqtt_parser.py",
            "realtime_dashboard.py",
            "realtime_fixed.py",
            "simple.py",
            "fixed_dashboard.py",
            "map_dashboard.py",
            "stable_dashboard.py"
        ]
    
    def create_backup_directory(self):
        """创建备份目录"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
            print(f"✅ 创建备份目录: {self.backup_dir}")
        else:
            print(f"📁 备份目录已存在: {self.backup_dir}")
    
    def backup_files(self):
        """备份旧文件"""
        backed_up = 0
        
        for filename in self.files_to_backup:
            if os.path.exists(filename):
                try:
                    # 添加时间戳到备份文件名
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_name = f"{os.path.splitext(filename)[0]}_{timestamp}{os.path.splitext(filename)[1]}"
                    backup_path = os.path.join(self.backup_dir, backup_name)
                    
                    shutil.move(filename, backup_path)
                    print(f"📦 备份文件: {filename} → {backup_path}")
                    backed_up += 1
                    
                except Exception as e:
                    print(f"❌ 备份文件失败 {filename}: {e}")
            else:
                print(f"⚠️ 文件不存在: {filename}")
        
        print(f"✅ 成功备份 {backed_up} 个文件")
    
    def show_current_structure(self):
        """显示当前项目结构"""
        print("\n📁 当前项目结构:")
        print("=" * 40)
        
        for item in sorted(os.listdir(".")):
            if item.startswith('.'):
                continue
                
            if os.path.isdir(item):
                print(f"📁 {item}/")
                # 显示目录内容
                try:
                    for subitem in sorted(os.listdir(item)):
                        if not subitem.startswith('.'):
                            print(f"   📄 {subitem}")
                except:
                    pass
            else:
                size = os.path.getsize(item)
                size_str = f"{size:,} bytes" if size < 1024 else f"{size/1024:.1f} KB"
                
                if item in self.current_files:
                    print(f"✅ {item:<30} ({size_str})")
                else:
                    print(f"📄 {item:<30} ({size_str})")
    
    def create_project_info(self):
        """创建项目信息文件"""
        info_content = f"""# 项目清理信息

## 清理时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 当前活跃文件
{chr(10).join(f"- {f}" for f in self.current_files)}

## 已备份文件
{chr(10).join(f"- {f}" for f in self.files_to_backup if os.path.exists(os.path.join(self.backup_dir, f)))}

## 项目架构
```
MQTT客户端 → EMQX → HTTP Bridge → 接收服务 → MySQL → 地图服务 → 百度地图
```

## 服务端口分配
- EMQX MQTT: 1883
- EMQX 管理: 18083  
- 接收服务: 5000
- 地图服务: 8081
- MySQL: 3306

## 快速启动
```bash
python3 service_manager.py start-all
python3 test_data_flow.py
```

## 访问地址
- 地图界面: http://101.200.72.188:8081
- EMQX管理: http://101.200.72.188:18083
"""
        
        with open("PROJECT_INFO.md", "w", encoding="utf-8") as f:
            f.write(info_content)
        
        print("✅ 创建项目信息文件: PROJECT_INFO.md")
    
    def run_cleanup(self):
        """执行完整清理"""
        print("🧹 开始项目文件清理...")
        print("=" * 50)
        
        # 显示清理前的结构
        print("📋 清理前的文件结构:")
        self.show_current_structure()
        
        # 创建备份目录
        self.create_backup_directory()
        
        # 备份旧文件
        print(f"\n📦 备份旧文件到 {self.backup_dir}/")
        self.backup_files()
        
        # 创建项目信息
        self.create_project_info()
        
        # 显示清理后的结构
        print(f"\n📋 清理后的文件结构:")
        self.show_current_structure()
        
        print("\n" + "=" * 50)
        print("✅ 项目清理完成！")
        print(f"📁 旧文件已备份到: {self.backup_dir}/")
        print("📄 项目信息已保存到: PROJECT_INFO.md")

def main():
    cleaner = ProjectCleaner()
    
    print("🧹 MQTT位置监控项目清理工具")
    print("=" * 40)
    print("此工具将:")
    print("1. 备份旧的开发文件")
    print("2. 保留当前使用的核心文件")
    print("3. 创建清晰的项目结构")
    print("4. 生成项目信息文档")
    print()
    
    response = input("是否继续清理? (y/N): ").lower().strip()
    
    if response in ['y', 'yes']:
        cleaner.run_cleanup()
    else:
        print("❌ 清理已取消")

if __name__ == "__main__":
    main()
