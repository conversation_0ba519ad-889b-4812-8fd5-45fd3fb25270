#!/usr/bin/env python3
"""
多气体地图标记显示测试脚本
测试地图标点显示多个气体浓度值的功能
"""

import json
import time
import requests
from datetime import datetime

# 测试数据 - 不同数量的气体组合
test_scenarios = [
    {
        "name": "单气体测试",
        "device_id": "single_gas_001",
        "device_name": "单气体检测点",
        "lat": 39.920,
        "lng": 116.410,
        "data": {
            "H2": 23730,  # 氢气 - 您的格式
            "temp": 28.5,
            "humidity": 65
        }
    },
    {
        "name": "双气体测试", 
        "device_id": "dual_gas_002",
        "device_name": "双气体检测点",
        "lat": 39.925,
        "lng": 116.415,
        "data": {
            "H2": 15000,   # 氢气
            "SF6": 1200,   # 六氟化硫
            "temp": 26.8,
            "pressure": 1.01
        }
    },
    {
        "name": "三气体测试",
        "device_id": "triple_gas_003", 
        "device_name": "三气体检测点",
        "lat": 39.915,
        "lng": 116.405,
        "data": {
            "CH4": 2500,   # 甲烷
            "CO": 85,      # 一氧化碳
            "H2S": 15,     # 硫化氢
            "temp": 31.2,
            "humidity": 72
        }
    },
    {
        "name": "多气体测试",
        "device_id": "multi_gas_004",
        "device_name": "多气体检测点", 
        "lat": 39.930,
        "lng": 116.420,
        "data": {
            "H2": 8000,    # 氢气
            "SF6": 800,    # 六氟化硫
            "CH4": 1500,   # 甲烷
            "CO": 45,      # 一氧化碳
            "NH3": 30,     # 氨气
            "SO2": 8,      # 二氧化硫
            "temp": 29.1,
            "humidity": 61
        }
    },
    {
        "name": "危险级别测试",
        "device_id": "danger_gas_005",
        "device_name": "危险级别检测点",
        "lat": 39.908,
        "lng": 116.398,
        "data": {
            "H2": 45000,   # 氢气 - 危险级别
            "CO": 150,     # 一氧化碳 - 危险级别
            "H2S": 35,     # 硫化氢 - 危险级别
            "CH4": 8000,   # 甲烷 - 危险级别
            "SF6": 6000,   # 六氟化硫 - 危险级别
            "temp": 35.6,
            "humidity": 85
        }
    },
    {
        "name": "混合浓度测试",
        "device_id": "mixed_gas_006",
        "device_name": "混合浓度检测点",
        "lat": 39.912,
        "lng": 116.425,
        "data": {
            "H2": 500,     # 氢气 - 正常
            "CO": 75,      # 一氧化碳 - 警告
            "CH4": 6000,   # 甲烷 - 危险
            "O2": 18.5,    # 氧气 - 警告
            "VOC": 1200,   # VOC - 危险
            "temp": 27.3
        }
    }
]

def send_test_data(server_ip="localhost", port=5000):
    """发送测试数据"""
    url = f"http://{server_ip}:{port}/mqtt/data"
    
    print("🧪 多气体地图标记显示测试")
    print("=" * 60)
    print("测试目标: 验证地图标点显示多个气体浓度值")
    print("预期效果:")
    print("- 单气体: 显示1个气体数值")
    print("- 双气体: 显示2个气体数值") 
    print("- 三气体: 显示3个气体数值")
    print("- 多气体: 显示前3个气体 + 剩余数量提示")
    print("- 颜色: 根据最高危险级别显示标记颜色")
    print("=" * 60)
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📡 发送测试场景 {i}/6: {scenario['name']}")
        
        # 构造完整的测试数据
        test_data = {
            "lat": scenario["lat"],
            "lng": scenario["lng"],
            "device_id": scenario["device_id"],
            "name": scenario["device_name"],
            "timestamp": datetime.now().isoformat()
        }
        test_data.update(scenario["data"])
        
        # 构造EMQX格式
        mqtt_data = {
            "client_id": scenario["device_id"],
            "topic": f"location/{scenario['device_id']}",
            "payload": json.dumps(test_data),
            "qos": 0,
            "retain": False,
            "timestamp": test_data["timestamp"]
        }
        
        try:
            response = requests.post(url, json=mqtt_data, timeout=10)
            if response.status_code == 200:
                print(f"✅ 数据发送成功: {scenario['device_name']}")
                
                # 显示包含的气体信息
                gas_info = []
                for key, value in scenario["data"].items():
                    if key.upper() in ['H2', 'SF6', 'CH4', 'CO', 'H2S', 'CO2', 'NH3', 'SO2', 'NO2', 'O2', 'O3', 'VOC', 'C2H6', 'C3H8', 'C4H10']:
                        gas_info.append(f"{key}: {value}")
                
                if gas_info:
                    print(f"   🔬 包含气体: {', '.join(gas_info)}")
                    print(f"   📊 气体数量: {len(gas_info)} 种")
                    
            else:
                print(f"❌ 数据发送失败: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")
        
        # 间隔发送
        if i < len(test_scenarios):
            time.sleep(2)
    
    print("\n" + "=" * 60)
    print("✅ 测试数据发送完成!")
    
    print("\n📋 验证检查清单:")
    print("1. 访问监控界面: http://服务器IP:8082")
    print("2. 查看地图上的6个测试设备标记")
    print("3. 验证标记显示效果:")
    print("   - single_gas_001: 显示 '氢气 23K ppm'")
    print("   - dual_gas_002: 显示 '氢气 15K ppm' + 'SF6 1.2K ppm'")
    print("   - triple_gas_003: 显示 '甲烷 2.5K ppm' + 'CO 85 ppm' + 'H2S 15 ppm'")
    print("   - multi_gas_004: 显示前3种气体 + '+3种' 提示")
    print("   - danger_gas_005: 红色标记(危险级别)")
    print("   - mixed_gas_006: 显示混合危险级别")
    print("4. 点击标记查看完整气体信息")
    print("5. 验证颜色预警是否正确")

def check_server_status(server_ip="localhost", port=5000):
    """检查服务器状态"""
    try:
        response = requests.get(f"http://{server_ip}:{port}/health", timeout=5)
        if response.status_code == 200:
            print("✅ MQTT接收服务运行正常")
            return True
        else:
            print(f"❌ 服务状态异常: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务: {e}")
        return False

def main():
    print("🚀 多气体地图标记显示测试工具")
    print("功能: 测试地图标点显示多个气体浓度值")
    print("=" * 60)
    
    # 获取服务器IP
    server_ip = input("请输入服务器IP (默认localhost): ").strip()
    if not server_ip:
        server_ip = "localhost"
    
    print(f"\n🔍 检查服务器状态: {server_ip}:5000")
    if not check_server_status(server_ip):
        print("请确保MQTT接收服务正在运行!")
        return
    
    print(f"\n📡 准备发送6个测试场景到: {server_ip}:5000")
    print("测试场景:")
    for i, scenario in enumerate(test_scenarios, 1):
        gas_count = sum(1 for key in scenario["data"].keys() 
                       if key.upper() in ['H2', 'SF6', 'CH4', 'CO', 'H2S', 'CO2', 'NH3', 'SO2', 'NO2', 'O2', 'O3', 'VOC'])
        print(f"  {i}. {scenario['name']} - {gas_count}种气体")
    
    confirm = input("\n确认开始测试? (y/N): ").strip().lower()
    
    if confirm == 'y':
        send_test_data(server_ip)
    else:
        print("测试已取消")

if __name__ == "__main__":
    main()
