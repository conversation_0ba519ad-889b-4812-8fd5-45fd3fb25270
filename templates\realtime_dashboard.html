<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT 实时数据监控</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { 
            background: rgba(255,255,255,0.95); 
            color: #2c3e50; 
            padding: 25px; 
            border-radius: 15px; 
            margin-bottom: 25px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .status { 
            display: inline-block; 
            padding: 5px 15px; 
            border-radius: 20px; 
            font-size: 0.9em; 
            margin-left: 15px; 
        }
        .status.connected { background: #2ecc71; color: white; }
        .status.disconnected { background: #e74c3c; color: white; }
        
        .stats-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            margin-bottom: 25px; 
        }
        .stat-card { 
            background: rgba(255,255,255,0.95); 
            padding: 20px; 
            border-radius: 15px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
            text-align: center; 
        }
        .stat-number { 
            font-size: 2.5em; 
            font-weight: bold; 
            color: #3498db;
        }
        .stat-label { color: #7f8c8d; margin-top: 8px; font-weight: 500; }
        
        .dashboard-grid { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 25px; 
            margin-bottom: 25px; 
        }
        .chart-container { 
            background: rgba(255,255,255,0.95); 
            padding: 25px; 
            border-radius: 15px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .chart-title { 
            font-size: 1.2em; 
            font-weight: bold; 
            margin-bottom: 15px; 
            color: #2c3e50; 
        }
        
        .messages-section { 
            background: rgba(255,255,255,0.95); 
            border-radius: 15px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
        }
        .messages-header { 
            background: linear-gradient(45deg, #667eea, #764ba2); 
            color: white; 
            padding: 20px; 
            border-radius: 15px 15px 0 0; 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
        }
        .refresh-btn { 
            background: rgba(255,255,255,0.2); 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 25px; 
            cursor: pointer; 
            transition: all 0.3s; 
        }
        .refresh-btn:hover { 
            background: rgba(255,255,255,0.3); 
            transform: translateY(-2px); 
        }
        
        .messages-list { max-height: 500px; overflow-y: auto; }
        .message { 
            padding: 20px; 
            border-bottom: 1px solid #ecf0f1; 
            transition: all 0.3s; 
        }
        .message:hover { background: #f8f9fa; }
        .message:last-child { border-bottom: none; }
        .message.new { 
            animation: slideIn 0.5s ease-out; 
            background: #e8f5e8; 
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        .message-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 10px; 
        }
        .message-topic { 
            font-weight: bold; 
            color: #2c3e50; 
            font-size: 1.1em; 
        }
        .message-time { color: #95a5a6; font-size: 0.9em; }
        .message-client { 
            color: #7f8c8d; 
            font-size: 0.9em; 
            margin-bottom: 8px; 
        }
        .message-payload { 
            background: #f8f9fa; 
            padding: 12px; 
            border-radius: 8px; 
            font-family: 'Courier New', monospace; 
            font-size: 0.9em; 
            border-left: 4px solid #3498db; 
        }
        
        .loading { text-align: center; padding: 40px; color: #7f8c8d; }
        canvas { max-height: 300px; }
        
        #realtime-data {
            height: 300px; 
            overflow-y: auto; 
            font-family: monospace; 
            font-size: 0.8em; 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 8px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 MQTT 实时数据监控面板</h1>
            <p>实时显示 MQTT 消息流和数据可视化</p>
            <span id="connection-status" class="status disconnected">连接中...</span>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="total-messages">-</div>
                <div class="stat-label">总消息数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="today-messages">-</div>
                <div class="stat-label">今日消息</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="active-topics">-</div>
                <div class="stat-label">活跃主题</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="active-clients">-</div>
                <div class="stat-label">活跃客户端</div>
            </div>
        </div>
        
        <div class="dashboard-grid">
            <div class="chart-container">
                <div class="chart-title">📊 消息频率趋势</div>
                <canvas id="messageChart"></canvas>
            </div>
            <div class="chart-container">
                <div class="chart-title">🔄 实时数据流</div>
                <div id="realtime-data">等待实时数据...</div>
            </div>
        </div>
        
        <div class="dashboard-grid">
            <div class="chart-container">
                <div class="chart-title">🌡️ 温度数据趋势</div>
                <canvas id="temperatureChart"></canvas>
            </div>
            <div class="chart-container">
                <div class="chart-title">📈 主题分布</div>
                <canvas id="topicChart"></canvas>
            </div>
        </div>
        
        <div class="messages-section">
            <div class="messages-header">
                <h2>📨 最新消息</h2>
                <button class="refresh-btn" onclick="loadData()">刷新数据</button>
            </div>
            <div id="messages-list" class="messages-list loading">
                正在加载数据...
            </div>
        </div>
    </div>

    <script>
        const socket = io();
        let messageChart, temperatureChart, topicChart;
        let realtimeData = [];
        let temperatureData = [];
        
        socket.on('connect', function() {
            document.getElementById('connection-status').textContent = '已连接';
            document.getElementById('connection-status').className = 'status connected';
            console.log('Connected to server');
        });
        
        socket.on('disconnect', function() {
            document.getElementById('connection-status').textContent = '连接断开';
            document.getElementById('connection-status').className = 'status disconnected';
        });
        
        socket.on('new_message', function(data) {
            console.log('New message:', data);
            
            const realtimeDiv = document.getElementById('realtime-data');
            const newEntry = `[${data.arrived}] ${data.topic}: ${data.payload}\n`;
            realtimeData.unshift(newEntry);
            if (realtimeData.length > 20) realtimeData.pop();
            realtimeDiv.textContent = realtimeData.join('');
            
            if (data.payload_json && (data.payload_json.temperature || data.payload_json.temp)) {
                const temp = data.payload_json.temperature || data.payload_json.temp;
                const time = new Date().toLocaleTimeString();
                
                temperatureData.push({x: time, y: temp});
                if (temperatureData.length > 20) temperatureData.shift();
                updateTemperatureChart();
            }
            
            addNewMessageToList(data);
            loadStats();
        });
        
        function addNewMessageToList(msg) {
            const messagesList = document.getElementById('messages-list');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message new';
            messageDiv.innerHTML = `
                <div class="message-header">
                    <div class="message-topic">${msg.topic}</div>
                    <div class="message-time">${msg.arrived}</div>
                </div>
                <div class="message-client">客户端: ${msg.client_id}</div>
                <div class="message-payload">${msg.payload}</div>
            `;
            
            messagesList.insertBefore(messageDiv, messagesList.firstChild);
            
            const messages = messagesList.querySelectorAll('.message');
            if (messages.length > 50) {
                messagesList.removeChild(messages[messages.length - 1]);
            }
        }
        
        function loadStats() {
            fetch('/api/messages')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('total-messages').textContent = data.data.length;
                        
                        const today = new Date().toISOString().split('T')[0];
                        const todayCount = data.data.filter(msg => msg.arrived.startsWith(today)).length;
                        document.getElementById('today-messages').textContent = todayCount;
                        
                        const topics = new Set(data.data.map(msg => msg.topic));
                        const clients = new Set(data.data.map(msg => msg.client_id));
                        document.getElementById('active-topics').textContent = topics.size;
                        document.getElementById('active-clients').textContent = clients.size;
                    }
                });
        }
        
        function loadMessages() {
            fetch('/api/messages')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const messagesList = document.getElementById('messages-list');
                        if (data.data.length === 0) {
                            messagesList.innerHTML = '<div class="loading">暂无消息数据</div>';
                            return;
                        }
                        
                        messagesList.innerHTML = data.data.map(msg => `
                            <div class="message">
                                <div class="message-header">
                                    <div class="message-topic">${msg.topic}</div>
                                    <div class="message-time">${msg.arrived}</div>
                                </div>
                                <div class="message-client">客户端: ${msg.client_id}</div>
                                <div class="message-payload">${msg.payload}</div>
                            </div>
                        `).join('');
                    }
                });
        }
        
        function loadChartData() {
            fetch('/api/chart_data')
                .then(response => response.json())
                .then(data => {
                    updateMessageChart(data.hourly || []);
                    updateTopicChart(data.topics || []);
                    if (data.temperature && data.temperature.length > 0) {
                        temperatureData = data.temperature;
                        updateTemperatureChart();
                    }
                });
        }
        
        function updateMessageChart(data) {
            const ctx = document.getElementById('messageChart').getContext('2d');
            if (messageChart) messageChart.destroy();
            
            messageChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(d => d.hour),
                    datasets: [{
                        label: '消息数量',
                        data: data.map(d => d.count),
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: { y: { beginAtZero: true } }
                }
            });
        }
        
        function updateTemperatureChart() {
            const ctx = document.getElementById('temperatureChart').getContext('2d');
            if (temperatureChart) temperatureChart.destroy();
            
            temperatureChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: temperatureData.map(d => d.x),
                    datasets: [{
                        label: '温度 (°C)',
                        data: temperatureData.map(d => d.y),
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: { y: { beginAtZero: false } }
                }
            });
        }
        
        function updateTopicChart(data) {
            const ctx = document.getElementById('topicChart').getContext('2d');
            if (topicChart) topicChart.destroy();
            
            topicChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: data.map(d => d.topic),
                    datasets: [{
                        data: data.map(d => d.count),
                        backgroundColor: [
                            '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
                            '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
        
        function loadData() {
            loadStats();
            loadMessages();
            loadChartData();
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            setInterval(loadChartData, 30000);
        });
    </script>
</body>
</html>
