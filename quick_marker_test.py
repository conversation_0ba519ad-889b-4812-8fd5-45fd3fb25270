#!/usr/bin/env python3
"""
快速标记测试脚本 - 验证修复效果
"""

import json
import requests
from datetime import datetime

def send_quick_test(server_ip="localhost"):
    """发送快速测试数据"""
    url = f"http://{server_ip}:5000/mqtt/data"
    
    # 您的氢气 + SF6 格式测试
    test_data = {
        "lat": 39.920,
        "lng": 116.410,
        "H2": 23730,      # 氢气 - 您的格式
        "SF6": 1200,      # 六氟化硫
        "device_id": "quick_test",
        "name": "快速测试设备",
        "timestamp": datetime.now().isoformat()
    }
    
    mqtt_data = {
        "client_id": "quick_test",
        "topic": "location/quick_test",
        "payload": json.dumps(test_data),
        "qos": 0,
        "retain": False,
        "timestamp": test_data["timestamp"]
    }
    
    try:
        response = requests.post(url, json=mqtt_data, timeout=10)
        if response.status_code == 200:
            print("✅ 测试数据发送成功!")
            print("📍 预期显示:")
            print("   氢气: 24K ppm")
            print("   六氟化硫: 1K ppm")
            print("🎯 访问 http://{}:8082 查看效果".format(server_ip))
            return True
        else:
            print(f"❌ 发送失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def main():
    print("🚀 快速标记测试")
    server_ip = input("服务器IP (默认localhost): ").strip() or "localhost"
    
    if send_quick_test(server_ip):
        print("\n✅ 请检查地图标记是否:")
        print("1. 显示两行气体信息")
        print("2. 小尖角指向正确位置")
        print("3. 标记大小适中")
        print("4. 文字清晰可读")

if __name__ == "__main__":
    main()
