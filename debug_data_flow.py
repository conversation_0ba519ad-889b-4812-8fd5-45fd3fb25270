#!/usr/bin/env python3
# Version: 1.0 | Status: Debug | Description: 数据流调试脚本
"""
数据流调试脚本
快速诊断为什么地图上没有数据显示
"""

import requests
import json
import mysql.connector
import paho.mqtt.client as mqtt
import time
from datetime import datetime

def test_mysql_data():
    """检查MySQL中的数据"""
    print("🗄️ 检查MySQL数据...")
    
    mysql_config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'emqx',
        'password': 'EmqxPass!123',
        'database': 'emqx_data'
    }
    
    try:
        conn = mysql.connector.connect(**mysql_config)
        cursor = conn.cursor(dictionary=True)
        
        # 检查总数据量
        cursor.execute("SELECT COUNT(*) as total FROM mqtt_messages")
        total = cursor.fetchone()['total']
        print(f"📊 总消息数: {total}")
        
        # 检查位置数据
        cursor.execute("SELECT COUNT(*) as location_count FROM mqtt_messages WHERE topic LIKE 'location/%'")
        location_count = cursor.fetchone()['location_count']
        print(f"📍 位置消息数: {location_count}")
        
        # 显示最新的位置数据
        cursor.execute("""
            SELECT id, client_id, topic, payload, arrived 
            FROM mqtt_messages 
            WHERE topic LIKE 'location/%' 
            ORDER BY arrived DESC 
            LIMIT 5
        """)
        
        recent_data = cursor.fetchall()
        if recent_data:
            print(f"📝 最新的{len(recent_data)}条位置数据:")
            for row in recent_data:
                try:
                    payload = json.loads(row['payload'])
                    lat = payload.get('latitude', 'N/A')
                    lng = payload.get('longitude', 'N/A')
                    name = payload.get('name', 'Unknown')
                    print(f"  {row['topic']:20} | {name:10} | {lat}, {lng} | {row['arrived']}")
                except:
                    print(f"  {row['topic']:20} | 数据解析失败: {row['payload'][:50]}")
        else:
            print("❌ 没有找到位置数据")

        # 🔍 检查地图API的正则表达式匹配
        print("\n🔍 检查地图API正则表达式匹配:")
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM mqtt_messages
            WHERE payload REGEXP '"lat(itude)?"|"lng"|"lon(gitude)?"'
        """)
        regex_count = cursor.fetchone()['count']
        print(f"📊 正则匹配的消息数: {regex_count}")

        # 显示实际的payload格式
        if recent_data:
            print("\n📄 实际payload格式示例:")
            for i, row in enumerate(recent_data[:2]):
                print(f"  消息{i+1}: {row['payload']}")

        # 测试修正的正则表达式
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM mqtt_messages
            WHERE payload LIKE '%latitude%' AND payload LIKE '%longitude%'
        """)
        like_count = cursor.fetchone()['count']
        print(f"📊 LIKE匹配的消息数: {like_count}")
        
        cursor.close()
        conn.close()
        return location_count > 0
        
    except Exception as e:
        print(f"❌ MySQL检查失败: {e}")
        return False

def test_map_api():
    """测试地图API"""
    print("\n🗺️ 检查地图API...")
    
    try:
        response = requests.get("http://127.0.0.1:8081/api/map-data", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                locations = data.get('locations', [])
                print(f"✅ 地图API正常，返回 {len(locations)} 个位置点")
                
                if locations:
                    print("📍 位置点详情:")
                    for i, loc in enumerate(locations[:3], 1):
                        print(f"  {i}. {loc.get('name', 'Unknown')} - {loc.get('latitude')}, {loc.get('longitude')}")
                else:
                    print("⚠️ API正常但没有位置数据")
                
                return len(locations) > 0
            else:
                print(f"❌ API返回错误: {data.get('message', 'Unknown')}")
                return False
        else:
            print(f"❌ API响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 地图API测试失败: {e}")
        return False

def test_receiver_service():
    """测试接收服务"""
    print("\n📡 检查接收服务...")
    
    try:
        # 健康检查
        response = requests.get("http://127.0.0.1:5000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 接收服务健康检查通过")
        else:
            print(f"❌ 接收服务健康检查失败: {response.status_code}")
            return False
        
        # 统计信息
        response = requests.get("http://127.0.0.1:5000/api/stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            if stats.get('status') == 'success':
                data = stats.get('data', {})
                print(f"📊 接收服务统计:")
                print(f"  总消息数: {data.get('total_messages', 0)}")
                print(f"  位置消息数: {data.get('location_messages', 0)}")
                print(f"  最新消息: {data.get('latest_message', 'N/A')}")
                return True
            else:
                print(f"❌ 统计API错误: {stats.get('message', 'Unknown')}")
        else:
            print(f"❌ 统计API响应异常: {response.status_code}")
        
        return False
        
    except Exception as e:
        print(f"❌ 接收服务测试失败: {e}")
        return False

def send_test_data():
    """发送测试数据"""
    print("\n📤 发送测试数据...")
    
    try:
        client = mqtt.Client()
        client.connect("127.0.0.1", 1883, 60)
        
        test_locations = [
            {
                "latitude": 39.9042,
                "longitude": 116.4074,
                "name": "天安门",
                "device_id": "debug_test_1",
                "timestamp": datetime.now().isoformat()
            },
            {
                "latitude": 39.9163,
                "longitude": 116.3972,
                "name": "故宫",
                "device_id": "debug_test_2",
                "timestamp": datetime.now().isoformat()
            }
        ]
        
        for i, location in enumerate(test_locations, 1):
            topic = f"location/debug_test_{i}"
            payload = json.dumps(location)
            
            result = client.publish(topic, payload)
            if result.rc == 0:
                print(f"✅ 发送测试数据 {i}: {location['name']}")
            else:
                print(f"❌ 发送失败 {i}: {result.rc}")
            
            time.sleep(1)
        
        client.disconnect()
        print(f"📡 已发送 {len(test_locations)} 条测试数据")
        return True
        
    except Exception as e:
        print(f"❌ 发送测试数据失败: {e}")
        return False

def main():
    print("🔍 数据流调试工具")
    print("=" * 50)
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 检查接收服务
    receiver_ok = test_receiver_service()
    
    # 2. 检查MySQL数据
    mysql_has_data = test_mysql_data()
    
    # 3. 检查地图API
    api_has_data = test_map_api()
    
    # 4. 如果没有数据，发送测试数据
    if not mysql_has_data:
        print("\n💡 没有找到位置数据，发送测试数据...")
        if send_test_data():
            print("\n⏳ 等待5秒后重新检查...")
            time.sleep(5)
            
            # 重新检查
            mysql_has_data = test_mysql_data()
            api_has_data = test_map_api()
    
    # 5. 诊断结果
    print("\n" + "=" * 50)
    print("🔍 诊断结果:")
    print(f"  📡 接收服务: {'✅ 正常' if receiver_ok else '❌ 异常'}")
    print(f"  🗄️ 数据库数据: {'✅ 有数据' if mysql_has_data else '❌ 无数据'}")
    print(f"  🗺️ 地图API: {'✅ 有数据' if api_has_data else '❌ 无数据'}")
    
    if mysql_has_data and api_has_data:
        print("\n🎉 数据流正常！刷新地图页面应该能看到位置标记")
        print("🌐 访问: http://101.200.72.188:8081")
    elif mysql_has_data and not api_has_data:
        print("\n⚠️ 数据库有数据但API无数据，可能是地图服务问题")
        print("💡 建议重启地图服务: python3 baidu_map_fixed.py")
    elif not mysql_has_data:
        print("\n⚠️ 数据库没有数据，可能是EMQX HTTP Bridge未配置")
        print("💡 建议:")
        print("  1. 配置EMQX HTTP Bridge: python3 check_emqx_bridge.py")
        print("  2. 或直接发送HTTP请求测试接收服务")
    
    print(f"\n🔄 如需重新测试，运行: python3 debug_data_flow.py")

if __name__ == "__main__":
    main()
