{"name": "EMQX Server", "host": "**************", "protocol": "sftp", "port": 22, "username": "root", "password": "@Ma13131542711", "remotePath": "/opt/mqtt-to-mysql", "localPath": "./", "uploadOnSave": true, "useTempFile": false, "openSsh": false, "downloadOnOpen": false, "syncMode": "update", "ignore": [".vscode/**", ".git/**", ".DS_Store", "node_modules/**", "__pycache__/**", "*.pyc", "*.log", "backup/**", "backup_files/**", "tools/**", "tests/**", "docs/**", "config/**", "*.bak", "*.tmp", "reorganize_project.py", "cleanup_project.py"], "watcher": {"files": "**/*", "autoUpload": true, "autoDelete": false}}