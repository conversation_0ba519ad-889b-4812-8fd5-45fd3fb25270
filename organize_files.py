#!/usr/bin/env python3
"""
项目文件整理脚本
整理当前项目，将不再使用的文件移动到相应目录
"""

import os
import shutil
from datetime import datetime

class ProjectOrganizer:
    def __init__(self):
        self.base_dir = "."
        
        # 目录结构
        self.directories = {
            'backup': '备份和旧版本文件',
            'tools': '开发和管理工具',
            'tests': '测试脚本',
            'docs': '文档文件'
        }
        
        # 文件分类
        self.file_mapping = {
            # 核心生产文件 (保留在根目录)
            'core': [
                'gas_pipeline_inspection.py',    # 主要Web监控界面 ⭐
                'receiver_improved.py',          # MQTT接收服务 ⭐
                'manage_services.sh',            # 服务管理脚本 ⭐
                'restart_services.sh',           # 重启脚本
                'clear_database.py',             # 数据库清空工具
                'README.md',                     # 主要文档
            ],
            
            # 开发和管理工具
            'tools': [
                'service_manager.py',            # 旧版服务管理器
                'deploy.py',                     # 部署脚本
                'emqx_bridge_config.py',         # EMQX配置工具
                'cleanup_project.py',           # 项目清理工具
                'reorganize_project.py',        # 重组脚本
                'start_services.py',            # 启动脚本
                'organize_files.py',            # 本脚本
            ],
            
            # 测试脚本
            'tests': [
                'test_data_flow.py',            # 数据流测试
                'test_realtime.py',             # 实时测试
                'check_server_status.py',       # 状态检查
                'check_emqx_bridge.py',         # EMQX桥接检查
                'check_mysql_schema.py',        # 数据库结构检查
                'debug_data_flow.py',           # 数据流调试
                'generate_hebei_data.py',       # 测试数据生成
            ],
            
            # 备份文件 (旧版本和不再使用)
            'backup': [
                'baidu_map_fixed.py',           # 旧版地图服务
                'map_dashboard.py',             # 旧版地图面板
                'map_dashboard_backup.py',      # 地图面板备份
                'receiver.py',                  # 旧版接收器
                'receiver_debug.py',            # 调试版接收器
                'receiver_simple.py',           # 简化版接收器
                'receiver_fixed.py',            # 修复版接收器
                'mqtt_final.py',                # 旧版MQTT处理
                'mqtt_parser.py',               # MQTT解析器
                'realtime_dashboard.py',        # 旧版实时面板
                'realtime_fixed.py',            # 修复版实时面板
                'simple.py',                    # 简化版本
                'simple_gas_inspection.py',    # 简化版巡检
                'fixed_dashboard.py',           # 修复版面板
                'stable_dashboard.py',          # 稳定版面板
                'quick_fix.py',                 # 快速修复脚本
            ],
            
            # 文档文件
            'docs': [
                'MQTT-Location-Dashboard-Documentation.md',  # 详细文档
            ],
            
            # 配置文件
            'config': [
                'run_remote_check.bat',         # Windows批处理文件
            ]
        }
    
    def create_directories(self):
        """创建目录结构"""
        print("📁 创建目录结构...")
        
        for dir_name, description in self.directories.items():
            if not os.path.exists(dir_name):
                os.makedirs(dir_name)
                print(f"✅ 创建目录: {dir_name}/ - {description}")
            else:
                print(f"📁 目录已存在: {dir_name}/")
    
    def move_files(self):
        """移动文件到相应目录"""
        print("\n📦 移动文件...")
        
        moved_count = 0
        
        for category, files in self.file_mapping.items():
            if category == 'core':
                continue  # 核心文件保留在根目录
                
            target_dir = category
            if category == 'config':
                target_dir = 'tools'  # 配置文件放到tools目录
                
            for filename in files:
                if os.path.exists(filename):
                    target_path = os.path.join(target_dir, filename)
                    try:
                        shutil.move(filename, target_path)
                        print(f"📦 {filename} → {target_dir}/")
                        moved_count += 1
                    except Exception as e:
                        print(f"❌ 移动失败 {filename}: {e}")
                else:
                    print(f"⚠️  文件不存在: {filename}")
        
        print(f"\n✅ 共移动了 {moved_count} 个文件")
    
    def create_directory_readme(self):
        """为每个目录创建README文件"""
        print("\n📝 创建目录说明文件...")
        
        readme_contents = {
            'backup': """# 备份和旧版本文件

这个目录包含项目开发过程中的旧版本文件和备份。

## 文件说明
- `baidu_map_fixed.py` - 旧版地图服务（已被gas_pipeline_inspection.py替代）
- `receiver_*.py` - 各种版本的接收器（保留作为备份）
- `*_backup.py`, `*_debug.py` - 开发过程中的备份文件

## 注意事项
- 这些文件可以安全删除
- 保留作为参考或回滚使用
- 定期清理以节省空间

## 清理建议
如果系统运行稳定超过1个月，可以删除以下类型的文件：
- `*_backup.py` - 备份文件
- `*_debug.py` - 调试版本
- `*_simple.py` - 简化版本
""",
            
            'tools': """# 开发和管理工具

这个目录包含项目开发和管理相关的工具脚本。

## 文件说明
- `service_manager.py` - 旧版服务管理器
- `deploy.py` - 部署脚本
- `emqx_bridge_config.py` - EMQX配置工具
- `cleanup_project.py` - 项目清理工具

## 使用方法
```bash
python3 tools/deploy.py
python3 tools/emqx_bridge_config.py
```
""",
            
            'tests': """# 测试脚本

这个目录包含各种测试和检查脚本。

## 文件说明
- `test_data_flow.py` - 完整数据流测试
- `check_server_status.py` - 服务器状态检查
- `debug_data_flow.py` - 数据流调试
- `generate_hebei_data.py` - 测试数据生成器

## 使用方法
```bash
python3 tests/test_data_flow.py
python3 tests/check_server_status.py
```
""",
            
            'docs': """# 项目文档

这个目录包含项目相关的文档文件。

## 文件说明
- `MQTT-Location-Dashboard-Documentation.md` - 详细技术文档

## 维护
- 随着项目更新及时更新文档
- 记录重要的配置变更
"""
        }
        
        for dir_name, content in readme_contents.items():
            readme_path = os.path.join(dir_name, 'README.md')
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"📝 创建: {readme_path}")
    
    def show_new_structure(self):
        """显示新的项目结构"""
        print("\n🗂️ 新的项目结构:")
        print("=" * 50)
        
        # 显示根目录核心文件
        print("📁 根目录 (核心生产文件):")
        for filename in self.file_mapping['core']:
            if os.path.exists(filename):
                print(f"  ✅ {filename}")
            else:
                print(f"  ❌ {filename} (不存在)")
        
        # 显示各个子目录
        for dir_name, description in self.directories.items():
            if os.path.exists(dir_name):
                files = os.listdir(dir_name)
                print(f"\n📁 {dir_name}/ ({description}):")
                for file in sorted(files):
                    if not file.startswith('.'):
                        print(f"  📄 {file}")
    
    def organize(self):
        """执行整理"""
        print("🗂️ 项目文件整理工具")
        print("=" * 50)
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 1. 创建目录
        self.create_directories()
        
        # 2. 移动文件
        self.move_files()
        
        # 3. 创建说明文件
        self.create_directory_readme()
        
        # 4. 显示新结构
        self.show_new_structure()
        
        print("\n" + "=" * 50)
        print("✅ 项目整理完成！")
        print()
        print("💡 使用说明:")
        print("  🚀 启动服务: ./manage_services.sh start-all")
        print("  🧪 运行测试: python3 tests/test_data_flow.py")
        print("  🔧 使用工具: python3 tools/deploy.py")
        print("  📚 查看文档: cat docs/README.md")
        print()
        print("🗑️ 清理建议:")
        print("  - backup/ 目录中的文件可以安全删除")
        print("  - 系统稳定后可以删除 tests/ 中的临时测试文件")

if __name__ == "__main__":
    organizer = ProjectOrganizer()
    organizer.organize()
