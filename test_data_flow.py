#!/usr/bin/env python3
"""
数据流测试脚本
测试从MQTT发布到地图显示的完整数据流
"""

import paho.mqtt.client as mqtt
import json
import time
import requests
import mysql.connector
from datetime import datetime
import random

class DataFlowTester:
    def __init__(self):
        self.mqtt_host = "127.0.0.1"
        self.mqtt_port = 1883
        self.mysql_config = {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'emqx',
            'password': 'EmqxPass!123',
            'database': 'emqx_data'
        }
        
    def test_mysql_connection(self):
        """测试MySQL连接"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
            conn.close()
            print("✅ MySQL连接正常")
            return True
        except Exception as e:
            print(f"❌ MySQL连接失败: {e}")
            return False
    
    def test_receiver_service(self):
        """测试接收服务"""
        try:
            response = requests.get("http://127.0.0.1:5000/health", timeout=5)
            if response.status_code == 200:
                print("✅ 接收服务运行正常")
                return True
            else:
                print(f"❌ 接收服务响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 接收服务连接失败: {e}")
            return False
    
    def test_map_service(self):
        """测试地图服务"""
        try:
            response = requests.get("http://127.0.0.1:8081", timeout=5)
            if response.status_code == 200:
                print("✅ 地图服务运行正常")
                return True
            else:
                print(f"❌ 地图服务响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 地图服务连接失败: {e}")
            return False
    
    def publish_test_data(self):
        """发布测试位置数据"""
        try:
            client = mqtt.Client()
            client.connect(self.mqtt_host, self.mqtt_port, 60)
            
            # 生成测试位置数据（北京周边）
            test_locations = [
                {"latitude": 39.9042, "longitude": 116.4074, "name": "天安门"},
                {"latitude": 39.9163, "longitude": 116.3972, "name": "故宫"},
                {"latitude": 39.9075, "longitude": 116.3974, "name": "王府井"},
                {"latitude": 39.8851, "longitude": 116.4056, "name": "前门"},
                {"latitude": 39.9289, "longitude": 116.3883, "name": "什刹海"}
            ]
            
            for i, location in enumerate(test_locations):
                # 添加一些随机偏移
                location["latitude"] += random.uniform(-0.01, 0.01)
                location["longitude"] += random.uniform(-0.01, 0.01)
                location["timestamp"] = datetime.now().isoformat()
                location["device_id"] = f"test_device_{i+1}"
                location["temperature"] = random.uniform(20, 30)
                
                topic = f"location/test_device_{i+1}"
                payload = json.dumps(location)
                
                result = client.publish(topic, payload)
                if result.rc == 0:
                    print(f"✅ 发布测试数据到 {topic}: {location['name']}")
                else:
                    print(f"❌ 发布失败: {result.rc}")
                
                time.sleep(1)  # 间隔1秒
            
            client.disconnect()
            print(f"📡 已发布 {len(test_locations)} 条测试数据")
            return True
            
        except Exception as e:
            print(f"❌ 发布测试数据失败: {e}")
            return False
    
    def check_database_data(self):
        """检查数据库中的数据"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor(dictionary=True)
            
            # 检查最新的数据
            cursor.execute("""
                SELECT * FROM mqtt_messages
                WHERE topic LIKE 'location/%'
                ORDER BY arrived DESC
                LIMIT 10
            """)
            
            results = cursor.fetchall()
            
            if results:
                print(f"📊 数据库中有 {len(results)} 条最新位置数据:")
                for row in results:
                    try:
                        payload = json.loads(row['payload'])
                        lat = payload.get('latitude', 'N/A')
                        lng = payload.get('longitude', 'N/A')
                        name = payload.get('name', 'Unknown')
                        print(f"  {row['topic']:20} | {name:10} | {lat:.4f}, {lng:.4f}")
                    except:
                        print(f"  {row['topic']:20} | 数据解析失败")
            else:
                print("❌ 数据库中没有找到位置数据")
            
            cursor.close()
            conn.close()
            return len(results) > 0
            
        except Exception as e:
            print(f"❌ 检查数据库数据失败: {e}")
            return False
    
    def test_map_api(self):
        """测试地图API"""
        try:
            response = requests.get("http://127.0.0.1:8081/api/map-data", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    locations = data.get('data', [])
                    print(f"✅ 地图API返回 {len(locations)} 个位置点")
                    return True
                else:
                    print(f"❌ 地图API返回错误: {data.get('message', 'Unknown')}")
                    return False
            else:
                print(f"❌ 地图API响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 地图API测试失败: {e}")
            return False
    
    def run_full_test(self):
        """运行完整测试"""
        print(f"🧪 开始完整数据流测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        tests = [
            ("MySQL连接", self.test_mysql_connection),
            ("接收服务", self.test_receiver_service),
            ("地图服务", self.test_map_service),
            ("发布测试数据", self.publish_test_data),
            ("检查数据库", self.check_database_data),
            ("地图API", self.test_map_api)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🔍 测试: {test_name}")
            if test_func():
                passed += 1
            else:
                print(f"⚠️ {test_name} 测试失败，可能影响后续测试")
        
        print("\n" + "=" * 60)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！数据流正常工作")
            print("🌐 访问地图: http://101.200.72.188:8081")
        else:
            print("⚠️ 部分测试失败，请检查相关服务")

def main():
    tester = DataFlowTester()
    tester.run_full_test()

if __name__ == "__main__":
    main()
