#!/usr/bin/env python3
"""
快速计数修复验证脚本
"""

import json
import requests
import time

def test_count_fix(server_ip="localhost"):
    """测试计数修复效果"""
    
    print("🔧 快速计数修复验证")
    print("=" * 40)
    
    # 1. 清空数据库
    print("1. 清空数据库...")
    try:
        response = requests.post(f"http://{server_ip}:8082/api/clear", timeout=10)
        if response.status_code == 200:
            print("   ✅ 数据库已清空")
        else:
            print(f"   ❌ 清空失败: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 清空出错: {e}")
        return
    
    time.sleep(2)
    
    # 2. 检查初始计数
    print("2. 检查初始计数...")
    try:
        response = requests.get(f"http://{server_ip}:8082/api/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            initial_count = stats.get('stats', {}).get('total', -1)
            print(f"   📊 初始计数: {initial_count}")
            if initial_count != 0:
                print("   ⚠️ 初始计数不为0，可能清空未完成")
        else:
            print(f"   ❌ 获取统计失败: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 获取统计出错: {e}")
        return
    
    # 3. 发送无效JSON (您提到的格式)
    print("3. 发送无效JSON...")
    invalid_data = {
        "client_id": "test_invalid",
        "topic": "location/test_invalid",
        "payload": '{"CH4": 1110, "lat": 39.87127, "lng": 117.94614,}',  # 多余逗号
        "qos": 0
    }
    
    try:
        response = requests.post(f"http://{server_ip}:5000/mqtt/data", json=invalid_data, timeout=10)
        print(f"   📡 响应状态: {response.status_code}")
        if response.status_code == 400:
            print("   ✅ 无效JSON被正确拒绝")
        else:
            print("   ❌ 无效JSON未被拒绝")
    except Exception as e:
        print(f"   ❌ 发送出错: {e}")
        return
    
    time.sleep(2)
    
    # 4. 检查计数是否仍为0
    print("4. 检查计数是否仍为0...")
    try:
        response = requests.get(f"http://{server_ip}:8082/api/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            after_invalid_count = stats.get('stats', {}).get('total', -1)
            print(f"   📊 发送无效JSON后计数: {after_invalid_count}")
            if after_invalid_count == 0:
                print("   ✅ 计数正确，无效数据未导致ID递增")
            else:
                print("   ❌ 计数错误，无效数据导致了ID递增")
        else:
            print(f"   ❌ 获取统计失败: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 获取统计出错: {e}")
        return
    
    # 5. 发送有效JSON
    print("5. 发送有效JSON...")
    valid_data = {
        "client_id": "test_valid",
        "topic": "location/test_valid", 
        "payload": '{"CH4": 1110, "lat": 39.87127, "lng": 117.94614}',  # 正确格式
        "qos": 0
    }
    
    try:
        response = requests.post(f"http://{server_ip}:5000/mqtt/data", json=valid_data, timeout=10)
        print(f"   📡 响应状态: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 有效JSON被正确接受")
        else:
            print("   ❌ 有效JSON被拒绝")
    except Exception as e:
        print(f"   ❌ 发送出错: {e}")
        return
    
    time.sleep(2)
    
    # 6. 检查最终计数
    print("6. 检查最终计数...")
    try:
        response = requests.get(f"http://{server_ip}:8082/api/stats", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            final_count = stats.get('stats', {}).get('total', -1)
            print(f"   📊 最终计数: {final_count}")
            if final_count == 1:
                print("   ✅ 计数正确，只有有效数据被计入")
                print("\n🎉 修复成功! ID计数问题已解决")
            else:
                print(f"   ❌ 计数错误，应该为1但实际为{final_count}")
                print("\n⚠️ 修复可能未完全生效")
        else:
            print(f"   ❌ 获取统计失败: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 获取统计出错: {e}")
        return
    
    print(f"\n📋 测试总结:")
    print(f"- 无效JSON格式会被拒绝，不会导致ID递增")
    print(f"- 有效JSON格式会被接受，正常计入统计")
    print(f"- 数据库计数与实际数据保持一致")

def main():
    print("🚀 快速计数修复验证")
    server_ip = input("服务器IP (默认localhost): ").strip() or "localhost"
    
    # 检查服务状态
    try:
        response = requests.get(f"http://{server_ip}:5000/health", timeout=5)
        if response.status_code != 200:
            print("❌ MQTT接收服务不可用")
            return
    except:
        print("❌ 无法连接到MQTT接收服务")
        return
    
    test_count_fix(server_ip)

if __name__ == "__main__":
    main()
