#!/usr/bin/env python3
"""
检查和配置 EMQX HTTP Bridge
"""

import requests
import json
from datetime import datetime

class EMQXBridgeChecker:
    def __init__(self, emqx_host="127.0.0.1", emqx_port=18083, username="admin", password="public"):
        self.base_url = f"http://{emqx_host}:{emqx_port}/api/v5"
        self.auth = (username, password)
        self.emqx_host = emqx_host
        self.emqx_port = emqx_port
        
    def test_emqx_connection(self):
        """测试EMQX连接"""
        try:
            response = requests.get(f"{self.base_url}/status", auth=self.auth, timeout=10)
            if response.status_code == 200:
                print("✅ EMQX连接成功")
                try:
                    status_data = response.json()
                    print(f"📊 EMQX状态: {status_data}")
                except json.JSONDecodeError:
                    print("📊 EMQX响应正常但JSON解析失败")
                return True
            else:
                print(f"❌ EMQX连接失败: HTTP {response.status_code}")
                print(f"响应内容: {response.text[:200]}")
                return False
        except Exception as e:
            print(f"❌ 无法连接到EMQX: {e}")
            print(f"🔍 检查EMQX是否运行在 {self.emqx_host}:{self.emqx_port}")
            return False
    
    def list_bridges(self):
        """列出所有桥接配置"""
        try:
            response = requests.get(f"{self.base_url}/bridges", auth=self.auth)
            if response.status_code == 200:
                bridges = response.json()
                print(f"🌉 找到 {len(bridges.get('data', []))} 个桥接配置:")
                
                for bridge in bridges.get('data', []):
                    status = "🟢 启用" if bridge.get('enable') else "🔴 禁用"
                    bridge_type = bridge.get('type', 'unknown')
                    name = bridge.get('name', 'unnamed')
                    url = bridge.get('url', 'N/A')
                    
                    print(f"  📋 {name}")
                    print(f"     类型: {bridge_type}")
                    print(f"     状态: {status}")
                    print(f"     URL: {url}")
                    print()
                
                return bridges.get('data', [])
            else:
                print(f"❌ 获取桥接配置失败: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ 获取桥接配置出错: {e}")
            return []
    
    def list_rules(self):
        """列出所有规则配置"""
        try:
            response = requests.get(f"{self.base_url}/rules", auth=self.auth)
            if response.status_code == 200:
                rules = response.json()
                print(f"📋 找到 {len(rules.get('data', []))} 个规则配置:")
                
                for rule in rules.get('data', []):
                    status = "🟢 启用" if rule.get('enable') else "🔴 禁用"
                    rule_id = rule.get('id', 'unnamed')
                    sql = rule.get('sql', 'N/A')
                    
                    print(f"  📝 {rule_id}")
                    print(f"     状态: {status}")
                    print(f"     SQL: {sql}")
                    print()
                
                return rules.get('data', [])
            else:
                print(f"❌ 获取规则配置失败: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ 获取规则配置出错: {e}")
            return []
    
    def create_webhook_bridge(self):
        """创建Webhook桥接"""
        bridge_config = {
            "type": "webhook",
            "name": "mqtt_to_mysql_bridge",
            "enable": True,
            "url": "http://127.0.0.1:5000/mqtt/data",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json"
            },
            "body": json.dumps({
                "client_id": "${clientid}",
                "topic": "${topic}",
                "payload": "${payload}",
                "timestamp": "${timestamp}",
                "qos": "${qos}"
            }),
            "connect_timeout": "15s",
            "request_timeout": "15s",
            "pool_type": "random",
            "pool_size": 8,
            "enable_pipelining": 100
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/bridges",
                json=bridge_config,
                auth=self.auth
            )
            
            if response.status_code in [200, 201]:
                print("✅ Webhook桥接创建成功")
                return True
            else:
                print(f"❌ 创建Webhook桥接失败: {response.status_code}")
                print(f"响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 创建Webhook桥接出错: {e}")
            return False
    
    def create_forwarding_rule(self):
        """创建转发规则"""
        rule_config = {
            "id": "forward_location_messages",
            "sql": "SELECT clientid, topic, payload, timestamp, qos FROM \"location/+\"",
            "actions": [
                {
                    "function": "bridge",
                    "args": {
                        "name": "mqtt_to_mysql_bridge"
                    }
                }
            ],
            "enable": True,
            "description": "转发location主题的消息到MySQL数据库"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/rules",
                json=rule_config,
                auth=self.auth
            )
            
            if response.status_code in [200, 201]:
                print("✅ 转发规则创建成功")
                return True
            else:
                print(f"❌ 创建转发规则失败: {response.status_code}")
                print(f"响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 创建转发规则出错: {e}")
            return False
    
    def setup_complete_bridge(self):
        """完整设置HTTP Bridge"""
        print("🔧 开始配置EMQX HTTP Bridge...")
        print("=" * 40)
        
        # 1. 测试连接
        if not self.test_emqx_connection():
            return False
        
        # 2. 检查现有配置
        print("\n📋 检查现有配置:")
        bridges = self.list_bridges()
        rules = self.list_rules()
        
        # 3. 创建桥接（如果不存在）
        bridge_exists = any(b.get('name') == 'mqtt_to_mysql_bridge' for b in bridges)
        if not bridge_exists:
            print("\n🌉 创建Webhook桥接...")
            if not self.create_webhook_bridge():
                return False
        else:
            print("\n✅ Webhook桥接已存在")
        
        # 4. 创建规则（如果不存在）
        rule_exists = any(r.get('id') == 'forward_location_messages' for r in rules)
        if not rule_exists:
            print("\n📝 创建转发规则...")
            if not self.create_forwarding_rule():
                return False
        else:
            print("\n✅ 转发规则已存在")
        
        print("\n🎉 HTTP Bridge配置完成！")
        print("📡 现在MQTT消息会自动转发到接收服务")
        return True

def main():
    print(f"🔍 EMQX HTTP Bridge 检查工具 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    checker = EMQXBridgeChecker()
    
    # 检查当前状态
    if checker.test_emqx_connection():
        print("\n📋 当前配置状态:")
        bridges = checker.list_bridges()
        rules = checker.list_rules()
        
        # 如果没有配置，提供设置选项
        if not bridges and not rules:
            print("\n⚠️ 没有找到HTTP Bridge配置")
            response = input("是否现在配置HTTP Bridge? (y/N): ").lower().strip()
            
            if response in ['y', 'yes']:
                checker.setup_complete_bridge()
            else:
                print("💡 稍后可以运行以下命令配置:")
                print("   python3 check_emqx_bridge.py")
        else:
            print("\n✅ 找到现有配置")
            
            # 检查是否有我们需要的桥接
            has_our_bridge = any(b.get('name') == 'mqtt_to_mysql_bridge' for b in bridges)
            has_our_rule = any(r.get('id') == 'forward_location_messages' for r in rules)
            
            if not has_our_bridge or not has_our_rule:
                print("⚠️ 缺少必要的桥接或规则配置")
                response = input("是否现在补充配置? (y/N): ").lower().strip()
                
                if response in ['y', 'yes']:
                    checker.setup_complete_bridge()
    
    print("\n💡 下一步:")
    print("1. 确保接收服务运行: python3 service_manager.py start receiver")
    print("2. 启动地图服务: python3 service_manager.py start map")
    print("3. 测试数据流: python3 test_data_flow.py")

if __name__ == "__main__":
    main()
