#!/usr/bin/env python3
"""
实时刷新功能测试脚本
用于测试MQTT消息的实时推送功能
"""

import time
import json
import requests
from datetime import datetime
import paho.mqtt.client as mqtt

def test_api():
    """测试API功能"""
    print("🔍 测试API功能...")
    
    # 测试获取最大消息ID
    try:
        response = requests.get('http://localhost:8082/api/max-message-id')
        data = response.json()
        print(f"最大消息ID: {data}")
    except Exception as e:
        print(f"获取最大消息ID失败: {e}")
    
    # 测试获取新消息
    try:
        response = requests.get('http://localhost:8082/api/inspection-data?since_id=0&limit=5')
        data = response.json()
        print(f"获取新消息: {data['status']}, 数量: {len(data.get('data', []))}")
        if data.get('data'):
            print(f"消息ID范围: {data['data'][-1]['id']} - {data['data'][0]['id']}")
    except Exception as e:
        print(f"获取新消息失败: {e}")

def send_test_message():
    """发送测试MQTT消息"""
    print("📤 发送测试MQTT消息...")
    
    try:
        client = mqtt.Client()
        client.connect("localhost", 1883, 60)
        
        # 发送测试消息
        test_data = {
            "lat": 39.920 + (time.time() % 100) * 0.001,  # 稍微变化位置
            "lng": 116.410 + (time.time() % 100) * 0.001,
            "gas": 2500 + int(time.time() % 1000),
            "temp": 25.0 + (time.time() % 10),
            "humidity": 60 + int(time.time() % 40),
            "pressure": 1013.2,
            "timestamp": datetime.now().isoformat()
        }
        
        message = json.dumps(test_data)
        topic = f"location/test_device_{int(time.time() % 10)}"
        
        client.publish(topic, message)
        client.disconnect()
        
        print(f"✅ 消息已发送到主题: {topic}")
        print(f"📄 消息内容: {message}")
        
    except Exception as e:
        print(f"❌ 发送消息失败: {e}")

def main():
    """主函数"""
    print("🚀 MQTT实时刷新功能测试")
    print("=" * 50)
    
    while True:
        print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')}")
        
        # 测试API
        test_api()
        
        # 发送测试消息
        send_test_message()
        
        print("\n等待10秒后继续测试...")
        print("按 Ctrl+C 退出")
        
        try:
            time.sleep(10)
        except KeyboardInterrupt:
            print("\n👋 测试结束")
            break

if __name__ == "__main__":
    main()
