#!/usr/bin/env python3
"""
快速修复脚本
解决当前发现的问题
"""

import subprocess
import time
import requests
import mysql.connector
from datetime import datetime

def run_cmd(cmd):
    """执行命令"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def fix_mysql_connection():
    """修复MySQL连接问题"""
    print("🔧 修复MySQL连接...")
    
    mysql_config = {
        'host': '127.0.0.1',
        'port': 3306,
        'user': 'emqx',
        'password': 'EmqxPass!123',
        'database': 'emqx_data'
    }
    
    try:
        # 测试连接
        conn = mysql.connector.connect(**mysql_config)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("SHOW TABLES LIKE 'mqtt_messages'")
        table_exists = cursor.fetchone() is not None
        
        if table_exists:
            # 检查字段
            cursor.execute("DESCRIBE mqtt_messages")
            columns = cursor.fetchall()
            column_names = [col[0] for col in columns]
            print(f"✅ 表字段: {column_names}")
            
            # 检查是否有arrived字段
            if 'arrived' not in column_names:
                print("⚠️ 缺少arrived字段，添加中...")
                cursor.execute("ALTER TABLE mqtt_messages ADD COLUMN arrived TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
                conn.commit()
                print("✅ 添加arrived字段成功")
        else:
            print("❌ mqtt_messages表不存在，创建中...")
            create_sql = """
            CREATE TABLE mqtt_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                client_id VARCHAR(255),
                topic VARCHAR(255),
                payload TEXT,
                qos INT DEFAULT 0,
                arrived TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_topic (topic),
                INDEX idx_arrived (arrived)
            )
            """
            cursor.execute(create_sql)
            conn.commit()
            print("✅ 创建mqtt_messages表成功")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ MySQL修复失败: {e}")
        return False

def start_receiver_service():
    """启动接收服务"""
    print("🚀 启动接收服务...")
    
    # 停止现有进程
    success, stdout, stderr = run_cmd("pkill -f 'python.*receiver'")
    time.sleep(2)
    
    # 启动新的接收服务
    try:
        process = subprocess.Popen([
            'python3', 'receiver_improved.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        time.sleep(3)  # 等待启动
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print(f"✅ 接收服务启动成功 (PID: {process.pid})")
            
            # 测试健康检查
            try:
                response = requests.get("http://127.0.0.1:5000/health", timeout=5)
                if response.status_code == 200:
                    print("✅ 健康检查通过")
                    return True
                else:
                    print(f"❌ 健康检查失败: {response.status_code}")
            except Exception as e:
                print(f"❌ 健康检查连接失败: {e}")
            
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 接收服务启动失败")
            print(f"错误: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ 启动接收服务出错: {e}")
        return False

def check_services():
    """检查所有服务状态"""
    print("🔍 检查服务状态...")
    
    services = [
        ("http://127.0.0.1:5000/health", "接收服务"),
        ("http://127.0.0.1:8081", "地图服务"),
        ("http://127.0.0.1:18083", "EMQX管理")
    ]
    
    all_ok = True
    for url, name in services:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name} 正常")
            else:
                print(f"❌ {name} 异常: {response.status_code}")
                all_ok = False
        except Exception as e:
            print(f"❌ {name} 连接失败: {e}")
            all_ok = False
    
    return all_ok

def main():
    print("🔧 快速修复工具")
    print("=" * 30)
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 修复MySQL
    if fix_mysql_connection():
        print("✅ MySQL修复完成")
    else:
        print("❌ MySQL修复失败")
        return
    
    # 2. 启动接收服务
    if start_receiver_service():
        print("✅ 接收服务启动完成")
    else:
        print("❌ 接收服务启动失败")
    
    # 3. 检查所有服务
    print("\n🔍 最终状态检查:")
    if check_services():
        print("\n🎉 所有服务正常！")
        print("📱 访问地址:")
        print("  🗺️ 地图: http://101.200.72.188:8081")
        print("  📊 统计: http://101.200.72.188:5000/api/stats")
    else:
        print("\n⚠️ 部分服务仍有问题，请手动检查")

if __name__ == "__main__":
    main()
