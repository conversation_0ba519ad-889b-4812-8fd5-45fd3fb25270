#!/bin/bash

# 天然气管道巡检系统 - 服务管理脚本
# 支持启动、停止、重启、状态查看等功能

WORK_DIR="/opt/mqtt-to-mysql"
WEB_SERVICE="gas_pipeline_inspection.py"
MQTT_SERVICE="receiver_improved.py"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查服务状态
check_service_status() {
    local service_name=$1
    local pid=$(pgrep -f "$service_name")
    if [ -n "$pid" ]; then
        echo -e "${GREEN}🟢 $service_name 正在运行 (PID: $pid)${NC}"
        return 0
    else
        echo -e "${RED}🔴 $service_name 未运行${NC}"
        return 1
    fi
}

# 停止服务
stop_service() {
    local service_name=$1
    print_info "停止 $service_name..."
    pkill -f "$service_name"
    sleep 2
    
    # 检查是否完全停止
    if pgrep -f "$service_name" > /dev/null; then
        print_warning "强制终止 $service_name..."
        pkill -9 -f "$service_name"
        sleep 1
    fi
    
    if ! pgrep -f "$service_name" > /dev/null; then
        print_success "$service_name 已停止"
    else
        print_error "$service_name 停止失败"
    fi
}

# 启动Web服务
start_web_service() {
    cd "$WORK_DIR" || {
        print_error "无法进入工作目录 $WORK_DIR"
        exit 1
    }
    
    print_info "启动 $WEB_SERVICE..."
    nohup python3 "$WEB_SERVICE" > gas_pipeline.log 2>&1 &
    local pid=$!
    sleep 3
    
    if ps -p $pid > /dev/null; then
        print_success "$WEB_SERVICE 启动成功 (PID: $pid)"
        print_info "访问地址: http://**************:8082"
    else
        print_error "$WEB_SERVICE 启动失败"
        print_info "查看错误日志: tail -20 $WORK_DIR/gas_pipeline.log"
    fi
}

# 启动MQTT服务
start_mqtt_service() {
    cd "$WORK_DIR" || {
        print_error "无法进入工作目录 $WORK_DIR"
        exit 1
    }
    
    print_info "启动 $MQTT_SERVICE..."
    nohup python3 "$MQTT_SERVICE" > receiver.log 2>&1 &
    local pid=$!
    sleep 2
    
    if ps -p $pid > /dev/null; then
        print_success "$MQTT_SERVICE 启动成功 (PID: $pid)"
    else
        print_error "$MQTT_SERVICE 启动失败"
        print_info "查看错误日志: tail -20 $WORK_DIR/receiver.log"
    fi
}

# 显示状态
show_status() {
    echo "================================================"
    echo "🔍 天然气管道巡检系统服务状态"
    echo "================================================"
    check_service_status "$WEB_SERVICE"
    check_service_status "$MQTT_SERVICE"
    echo ""
    
    echo "📊 系统资源使用情况:"
    echo "----------------------------------------"
    ps aux | grep -E "(gas_pipeline|receiver)" | grep -v grep | grep -v manage_services
    echo ""
    
    echo "📝 日志文件:"
    echo "----------------------------------------"
    echo "  Web服务: $WORK_DIR/gas_pipeline.log"
    echo "  MQTT服务: $WORK_DIR/receiver.log"
    echo ""
}

# 显示帮助
show_help() {
    echo "天然气管道巡检系统 - 服务管理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start-web     启动Web服务"
    echo "  start-mqtt    启动MQTT接收服务"
    echo "  start-all     启动所有服务"
    echo "  stop-web      停止Web服务"
    echo "  stop-mqtt     停止MQTT接收服务"
    echo "  stop-all      停止所有服务"
    echo "  restart-web   重启Web服务"
    echo "  restart-mqtt  重启MQTT接收服务"
    echo "  restart-all   重启所有服务"
    echo "  status        查看服务状态"
    echo "  logs-web      查看Web服务日志"
    echo "  logs-mqtt     查看MQTT服务日志"
    echo "  help          显示此帮助信息"
    echo ""
}

# 主逻辑
case "$1" in
    "start-web")
        start_web_service
        ;;
    "start-mqtt")
        start_mqtt_service
        ;;
    "start-all")
        start_web_service
        start_mqtt_service
        ;;
    "stop-web")
        stop_service "$WEB_SERVICE"
        ;;
    "stop-mqtt")
        stop_service "$MQTT_SERVICE"
        ;;
    "stop-all")
        stop_service "$WEB_SERVICE"
        stop_service "$MQTT_SERVICE"
        ;;
    "restart-web")
        stop_service "$WEB_SERVICE"
        start_web_service
        ;;
    "restart-mqtt")
        stop_service "$MQTT_SERVICE"
        start_mqtt_service
        ;;
    "restart-all")
        print_info "重启所有服务..."
        stop_service "$WEB_SERVICE"
        stop_service "$MQTT_SERVICE"
        echo ""
        start_web_service
        start_mqtt_service
        ;;
    "status")
        show_status
        ;;
    "logs-web")
        tail -f "$WORK_DIR/gas_pipeline.log"
        ;;
    "logs-mqtt")
        tail -f "$WORK_DIR/receiver.log"
        ;;
    "help"|"")
        show_help
        ;;
    *)
        print_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
