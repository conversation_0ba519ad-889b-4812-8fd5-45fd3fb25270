#!/usr/bin/env python3
"""
生成河北省范围内的随机天然气管道巡检数据
"""

import json
import mysql.connector
import random
from datetime import datetime, timedelta

# 河北省主要城市和区域的坐标范围
HEBEI_LOCATIONS = {
    "石家庄市": {"lat_range": (38.0, 38.2), "lng_range": (114.4, 114.6)},
    "唐山市": {"lat_range": (39.5, 39.8), "lng_range": (118.0, 118.3)},
    "秦皇岛市": {"lat_range": (39.8, 40.1), "lng_range": (119.5, 119.8)},
    "邯郸市": {"lat_range": (36.5, 36.8), "lng_range": (114.4, 114.7)},
    "邢台市": {"lat_range": (37.0, 37.3), "lng_range": (114.4, 114.7)},
    "保定市": {"lat_range": (38.8, 39.1), "lng_range": (115.4, 115.7)},
    "张家口市": {"lat_range": (40.7, 41.0), "lng_range": (114.8, 115.1)},
    "承德市": {"lat_range": (40.9, 41.2), "lng_range": (117.8, 118.1)},
    "沧州市": {"lat_range": (38.2, 38.5), "lng_range": (116.8, 117.1)},
    "廊坊市": {"lat_range": (39.4, 39.7), "lng_range": (116.6, 116.9)},
    "衡水市": {"lat_range": (37.7, 38.0), "lng_range": (115.6, 115.9)}
}

# 管道段名称
PIPELINE_SECTIONS = [
    "主干线A段", "主干线B段", "主干线C段", "支线1", "支线2", "支线3",
    "东线管道", "西线管道", "南线管道", "北线管道", "环线东段", "环线西段",
    "工业区管道", "居民区管道", "商业区管道", "开发区管道"
]

def generate_random_location():
    """生成河北省范围内的随机坐标"""
    city = random.choice(list(HEBEI_LOCATIONS.keys()))
    city_info = HEBEI_LOCATIONS[city]
    
    lat = random.uniform(city_info["lat_range"][0], city_info["lat_range"][1])
    lng = random.uniform(city_info["lng_range"][0], city_info["lng_range"][1])
    
    return lat, lng, city

def generate_random_time():
    """生成随机时间（最近30天内）"""
    now = datetime.now()
    days_ago = random.randint(0, 30)
    hours_ago = random.randint(0, 23)
    minutes_ago = random.randint(0, 59)
    
    random_time = now - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)
    return random_time

def generate_gas_concentration():
    """生成随机气体浓度值（0-1000000 ppm）"""
    # 大部分数据在正常范围内
    if random.random() < 0.7:  # 70%正常
        return random.randint(0, 500)
    elif random.random() < 0.9:  # 20%警告
        return random.randint(500, 2000)
    else:  # 10%危险
        return random.randint(2000, 1000000)

def generate_test_data():
    """生成100条测试数据"""
    test_data = []
    
    for i in range(100):
        lat, lng, city = generate_random_location()
        timestamp = generate_random_time()
        gas_concentration = generate_gas_concentration()
        
        # 随机选择数据格式
        format_type = random.choice(['json', 'array', 'string'])
        
        data = {
            "client_id": f"inspector_{i+1:03d}",
            "topic": f"pipeline/{random.choice(PIPELINE_SECTIONS).replace(' ', '_').lower()}",
            "timestamp": timestamp
        }
        
        if format_type == 'json':
            # JSON格式（精简字段名）
            data["payload"] = {
                "lat": round(lat, 6),
                "lng": round(lng, 6),
                "t": round(random.uniform(15, 35), 1),  # 温度
                "p": round(random.uniform(1.5, 4.0), 2),  # 压力
                "g": gas_concentration,  # 气体浓度
                "h": random.randint(30, 80),  # 湿度
                "ts": int(timestamp.timestamp())
            }
        elif format_type == 'array':
            # 数组格式（最省流量）
            data["payload"] = [
                round(lat, 6),
                round(lng, 6),
                round(random.uniform(15, 35), 1),
                round(random.uniform(1.5, 4.0), 2),
                gas_concentration,
                random.randint(30, 80),
                int(timestamp.timestamp())
            ]
        else:
            # 字符串格式
            data["payload"] = f"{lat:.6f},{lng:.6f},{random.uniform(15, 35):.1f},{random.uniform(1.5, 4.0):.2f},{gas_concentration},{random.randint(30, 80)}"
        
        test_data.append(data)
    
    return test_data

def insert_to_database(test_data):
    """插入数据到数据库"""
    try:
        conn = mysql.connector.connect(
            host='127.0.0.1',
            user='emqx',
            password='EmqxPass!123',
            database='emqx_data'
        )
        
        cursor = conn.cursor()
        
        for data in test_data:
            insert_sql = """
                INSERT INTO mqtt_messages (client_id, topic, payload, qos, arrived)
                VALUES (%s, %s, %s, %s, %s)
            """
            
            values = (
                data['client_id'],
                data['topic'],
                json.dumps(data['payload']) if isinstance(data['payload'], (dict, list)) else data['payload'],
                0,
                data['timestamp']
            )
            
            cursor.execute(insert_sql, values)
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"✅ 成功插入 {len(test_data)} 条河北省测试数据")
        return True
        
    except Exception as e:
        print(f"❌ 数据库插入失败: {e}")
        return False

if __name__ == '__main__':
    print("🚀 开始生成河北省天然气管道巡检测试数据...")
    
    # 生成数据
    test_data = generate_test_data()
    print(f"📊 已生成 {len(test_data)} 条测试数据")
    
    # 显示数据样本
    print("\n📋 数据样本:")
    for i, sample in enumerate(test_data[:3], 1):
        print(f"样本{i}: {sample}")
    
    # 插入数据库
    if insert_to_database(test_data):
        print("🎉 河北省测试数据生成完成！")
        print("📍 数据覆盖河北省11个主要城市")
        print("🔥 气体浓度范围: 0-1000000 ppm")
        print("⏰ 时间范围: 最近30天")
    else:
        print("❌ 数据插入失败")
