#!/usr/bin/env python3
"""
多气体检测功能测试脚本
测试新增的气体类型检测和显示功能
"""

import json
import time
import requests
from datetime import datetime

# 测试数据 - 包含多种气体类型
test_data_samples = [
    {
        "device_id": "gas_detector_001",
        "name": "天然气管道检测点A",
        "lat": 39.920,
        "lng": 116.410,
        "temp": 28.5,
        "humidity": 65,
        "pressure": 1.02,
        "CH4": 2500,    # 甲烷
        "H2": 23730,    # 氢气 - 您的格式
        "CO": 15,       # 一氧化碳
        "timestamp": datetime.now().isoformat()
    },
    {
        "device_id": "gas_detector_002", 
        "name": "SF6设备监测点B",
        "lat": 39.925,
        "lng": 116.415,
        "temp": 26.8,
        "humidity": 58,
        "pressure": 1.01,
        "SF6": 1200,    # 六氟化硫 - 新增支持
        "H2S": 8,       # 硫化氢
        "CO2": 800,     # 二氧化碳
        "timestamp": datetime.now().isoformat()
    },
    {
        "device_id": "gas_detector_003",
        "name": "工业区监测点C", 
        "lat": 39.915,
        "lng": 116.405,
        "temp": 31.2,
        "humidity": 72,
        "pressure": 0.98,
        "NH3": 25,      # 氨气
        "SO2": 5,       # 二氧化硫
        "NO2": 3,       # 二氧化氮
        "VOC": 300,     # 挥发性有机物
        "timestamp": datetime.now().isoformat()
    },
    {
        "device_id": "gas_detector_004",
        "name": "综合监测点D",
        "lat": 39.930,
        "lng": 116.420,
        "temp": 29.1,
        "humidity": 61,
        "pressure": 1.05,
        "O2": 20.9,     # 氧气
        "O3": 0.15,     # 臭氧
        "C2H6": 800,    # 乙烷
        "C3H8": 600,    # 丙烷
        "C4H10": 400,   # 丁烷
        "pm25": 45,     # PM2.5
        "pm10": 78,     # PM10
        "noise": 65,    # 噪声
        "timestamp": datetime.now().isoformat()
    },
    {
        "device_id": "gas_detector_005",
        "name": "危险气体监测点E",
        "lat": 39.908,
        "lng": 116.398,
        "temp": 35.6,
        "humidity": 85,
        "pressure": 0.95,
        "CH4": 8000,    # 高浓度甲烷 - 危险级别
        "H2": 45000,    # 高浓度氢气 - 危险级别
        "CO": 150,      # 高浓度一氧化碳 - 危险级别
        "H2S": 35,      # 高浓度硫化氢 - 危险级别
        "timestamp": datetime.now().isoformat()
    }
]

def send_mqtt_data(server_ip="localhost", port=5000):
    """发送测试数据到MQTT接收服务"""
    url = f"http://{server_ip}:{port}/mqtt/data"
    
    print("🧪 开始多气体检测功能测试")
    print("=" * 50)
    
    for i, data in enumerate(test_data_samples, 1):
        print(f"\n📡 发送测试数据 {i}/5: {data['name']}")
        
        # 构造EMQX格式的数据
        mqtt_data = {
            "client_id": data["device_id"],
            "topic": f"location/{data['device_id']}",
            "payload": json.dumps(data),
            "qos": 0,
            "retain": False,
            "timestamp": data["timestamp"]
        }
        
        try:
            response = requests.post(url, json=mqtt_data, timeout=10)
            if response.status_code == 200:
                print(f"✅ 数据发送成功: {data['device_id']}")
                
                # 显示包含的气体类型
                gas_types = []
                for key, value in data.items():
                    if key.upper() in ['CH4', 'H2', 'CO', 'CO2', 'H2S', 'SF6', 'NH3', 'SO2', 'NO2', 'O2', 'O3', 'C2H6', 'C3H8', 'C4H10', 'VOC']:
                        gas_types.append(f"{key}: {value}")
                
                if gas_types:
                    print(f"   🔬 检测气体: {', '.join(gas_types)}")
                    
            else:
                print(f"❌ 数据发送失败: HTTP {response.status_code}")
                print(f"   响应: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")
        
        # 间隔发送
        if i < len(test_data_samples):
            time.sleep(2)
    
    print("\n" + "=" * 50)
    print("✅ 测试数据发送完成!")
    print("\n📋 测试结果验证:")
    print("1. 访问监控界面: http://服务器IP:8082")
    print("2. 检查地图上是否显示5个测试设备")
    print("3. 点击设备标记查看气体检测数据")
    print("4. 验证中文名称和单位显示是否正确")
    print("5. 检查危险级别颜色标识")
    
    print("\n🔍 预期显示效果:")
    print("- 甲烷 显示为 '甲烷: 2500 ppm'")
    print("- H2 显示为 '氢气: 23730 ppm'") 
    print("- SF6 显示为 '六氟化硫: 1200 ppm'")
    print("- 危险级别气体显示红色标记")
    print("- 正常级别气体显示绿色标记")

def check_server_status(server_ip="localhost", port=5000):
    """检查服务器状态"""
    try:
        response = requests.get(f"http://{server_ip}:{port}/health", timeout=5)
        if response.status_code == 200:
            print("✅ MQTT接收服务运行正常")
            return True
        else:
            print(f"❌ 服务状态异常: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务: {e}")
        return False

def main():
    print("🚀 多气体检测功能测试工具")
    print("支持气体类型: CH4, H2, CO, CO2, H2S, SF6, NH3, SO2, NO2, O2, O3, VOC等")
    print("=" * 60)
    
    # 获取服务器IP
    server_ip = input("请输入服务器IP (默认localhost): ").strip()
    if not server_ip:
        server_ip = "localhost"
    
    print(f"\n🔍 检查服务器状态: {server_ip}:5000")
    if not check_server_status(server_ip):
        print("请确保MQTT接收服务正在运行!")
        return
    
    print(f"\n📡 准备发送测试数据到: {server_ip}:5000")
    confirm = input("确认开始测试? (y/N): ").strip().lower()
    
    if confirm == 'y':
        send_mqtt_data(server_ip)
    else:
        print("测试已取消")

if __name__ == "__main__":
    main()
