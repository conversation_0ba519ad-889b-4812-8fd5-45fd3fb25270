#!/usr/bin/env python3
import mysql.connector
from flask import Flask, render_template
from flask_socketio import <PERSON>cket<PERSON>, emit
import json
from datetime import datetime
import threading
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'mqtt-dashboard-secret'
socketio = SocketIO(app, cors_allowed_origins="*", logger=True, engineio_logger=True)

mysql_config = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'emqx',
    'password': 'EmqxPass!123',
    'database': 'emqx_data'
}

latest_message_id = 0

@app.route('/')
def dashboard():
    return render_template('realtime_dashboard.html')

@app.route('/api/messages')
def get_messages():
    try:
        conn = mysql.connector.connect(**mysql_config)
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("""
            SELECT id, client_id, topic, payload, arrived 
            FROM mqtt_messages 
            ORDER BY arrived DESC 
            LIMIT 50
        """)
        
        messages = cursor.fetchall()
        
        for msg in messages:
            msg['arrived'] = msg['arrived'].strftime('%Y-%m-%d %H:%M:%S')
            try:
                msg['payload_json'] = json.loads(msg['payload'])
            except:
                msg['payload_json'] = None
        
        cursor.close()
        conn.close()
        
        return {'status': 'success', 'data': messages}
    except Exception as e:
        return {'status': 'error', 'message': str(e)}

@app.route('/api/stats')
def get_stats():
    try:
        conn = mysql.connector.connect(**mysql_config)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM mqtt_messages")
        total_messages = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM mqtt_messages WHERE DATE(arrived) = CURDATE()")
        today_messages = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT topic) FROM mqtt_messages")
        active_topics = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT client_id) FROM mqtt_messages")
        active_clients = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        return {
            'total_messages': total_messages,
            'today_messages': today_messages,
            'active_topics': active_topics,
            'active_clients': active_clients
        }
    except Exception as e:
        return {'error': str(e)}

def check_new_messages():
    """后台线程检查新消息"""
    global latest_message_id
    
    # 获取当前最大 ID
    try:
        conn = mysql.connector.connect(**mysql_config)
        cursor = conn.cursor()
        cursor.execute("SELECT COALESCE(MAX(id), 0) FROM mqtt_messages")
        latest_message_id = cursor.fetchone()[0]
        cursor.close()
        conn.close()
        print(f"Starting from message ID: {latest_message_id}")
    except Exception as e:
        print(f"Error getting initial message ID: {e}")
    
    while True:
        try:
            conn = mysql.connector.connect(**mysql_config)
            cursor = conn.cursor(dictionary=True)
            
            cursor.execute("""
                SELECT id, client_id, topic, payload, arrived 
                FROM mqtt_messages 
                WHERE id > %s
                ORDER BY id ASC
            """, (latest_message_id,))
            
            new_messages = cursor.fetchall()
            
            for msg in new_messages:
                msg['arrived'] = msg['arrived'].strftime('%Y-%m-%d %H:%M:%S')
                try:
                    msg['payload_json'] = json.loads(msg['payload'])
                except:
                    msg['payload_json'] = None
                
                print(f"Broadcasting new message: ID {msg['id']}, Topic: {msg['topic']}")
                socketio.emit('new_message', msg, broadcast=True)
                latest_message_id = msg['id']
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"Error checking new messages: {e}")
        
        time.sleep(3)  # 每3秒检查一次

@socketio.on('connect')
def handle_connect():
    print('Client connected')
    emit('status', {'msg': 'Connected to MQTT Dashboard'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')

if __name__ == '__main__':
    # 启动后台线程
    thread = threading.Thread(target=check_new_messages)
    thread.daemon = True
    thread.start()
    
    print("Starting SocketIO server on port 8080...")
    socketio.run(app, host='0.0.0.0', port=8080, debug=False)
