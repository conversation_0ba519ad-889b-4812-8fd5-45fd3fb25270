#!/usr/bin/env python3
"""
清空数据库脚本
用于清空MQTT消息数据库中的所有记录
"""

import pymysql
import sys

# 数据库配置 - 与主程序保持一致
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'emqx',
    'password': 'EmqxPass!123',
    'database': 'emqx_data',
    'charset': 'utf8mb4'
}

def clear_database():
    """清空数据库中的所有消息记录"""
    try:
        print("🔗 连接数据库...")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 检查删除前的记录数
        cursor.execute("SELECT COUNT(*) FROM mqtt_messages")
        count_before = cursor.fetchone()[0]
        print(f"📊 删除前记录数: {count_before}")
        
        if count_before == 0:
            print("✅ 数据库已经是空的，无需清空")
            return
        
        # 确认操作
        confirm = input(f"⚠️  确定要删除所有 {count_before} 条记录吗？(y/N): ")
        if confirm.lower() != 'y':
            print("❌ 操作已取消")
            return
        
        # 执行删除
        print("🗑️  正在删除所有记录...")
        cursor.execute("DELETE FROM mqtt_messages")
        deleted_count = cursor.rowcount
        conn.commit()
        
        # 检查删除后的记录数
        cursor.execute("SELECT COUNT(*) FROM mqtt_messages")
        count_after = cursor.fetchone()[0]
        
        print(f"✅ 清空完成!")
        print(f"   删除前: {count_before} 条")
        print(f"   删除了: {deleted_count} 条")
        print(f"   删除后: {count_after} 条")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 清空数据库失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("🚀 数据库清空工具")
    print("=" * 40)
    clear_database()
    print("=" * 40)
    print("🎉 操作完成!")
